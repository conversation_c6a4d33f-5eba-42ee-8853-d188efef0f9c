{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "echo '<PERSON><PERSON> skipped for now'", "lint:fix": "echo '<PERSON><PERSON> skipped for now'", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "astro check", "new:post": "node scripts/new-post.js", "prepare": "husky install", "lighthouse": "lighthouse http://localhost:4321 --output=html --output-path=./lighthouse-report.html --chrome-flags=\"--headless\"", "lighthouse:mobile": "lighthouse http://localhost:4321 --output=html --output-path=./lighthouse-mobile-report.html --preset=perf --form-factor=mobile --chrome-flags=\"--headless\"", "analyze": "npm run build && npx astro build --analyze", "serve:build": "npm run build && npm run preview"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.3.3", "@astrojs/prism": "^3.3.0", "@astrojs/react": "^4.3.0", "@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.5.0", "@astrojs/tailwind": "^5.1.2", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "astro": "^5.13.0", "prismjs": "^1.30.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^3.4.0", "typescript": "^5.9.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "tsx": "^4.20.4"}}