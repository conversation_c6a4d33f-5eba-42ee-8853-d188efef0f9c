// @ts-check
import { defineConfig } from 'astro/config';

import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import react from '@astrojs/react';
import sitemap from '@astrojs/sitemap';

// https://astro.build/config
export default defineConfig({
  site: 'https://creatoraitools.github.io', // Update this to your actual domain
  base: '/creatoraitools', // Update if deploying to a subdirectory
  output: 'static',

  vite: {
    build: {
      cssMinify: true,
      minify: true,
    },
  },

  integrations: [
    tailwind(),
    mdx({
      syntaxHighlight: 'shiki',
      shikiConfig: {
        theme: 'github-dark-dimmed',
        wrap: true,
      },
    }),
    react({
      include: ['**/interactive/**'],
    }),
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date(),
    }),
  ],

  markdown: {
    shikiConfig: {
      theme: 'github-dark-dimmed',
      wrap: true,
    },
  },

  image: {
    domains: ['creatoraitools.github.io'],
    remotePatterns: [
      {
        protocol: 'https',
      },
    ],
  },

  compressHTML: true,
  build: {
    inlineStylesheets: 'auto',
  },
});
