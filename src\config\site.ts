export const SITE = {
  name: 'CreatorAITools',
  title: 'CreatorAITools - Micro-niche playbooks for AI-powered creators',
  description:
    'Discover practical AI tools and strategies for content creators. Learn how to leverage AI for writing, thumbnails, video automation, and SEO optimization.',
  url: 'https://creatoraitools.github.io',
  githubUrl: 'https://github.com/creatoraitools/creatoraitools',
  author: 'CreatorAITools Team',
  tagline: 'Micro-niche playbooks for AI-powered creators',

  // Social links
  social: {
    twitter: 'https://twitter.com/creatoraitools',
    github: 'https://github.com/creatoraitools',
    email: '<EMAIL>',
  },

  // SEO defaults
  defaultOgImage: '/og-default.png',
  postsPerPage: 12,

  // Categories
  categories: ['AI Writing', 'Thumbnails', 'Video Automation', 'SEO for Creators'] as const,

  // Navigation
  navigation: [
    { name: 'Home', href: '/' },
    { name: 'Blog', href: '/blog' },
    { name: 'Tags', href: '/tags' },
    { name: 'Backlinks', href: '/backlinks' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
  ],
} as const;

export type SiteConfig = typeof SITE;
export type Category = (typeof SITE.categories)[number];
