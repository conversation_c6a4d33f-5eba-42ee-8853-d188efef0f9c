import rss from '@astrojs/rss';
import { getCollection, type CollectionEntry } from 'astro:content';
import { SITE } from '../config/site';
import type { APIContext } from 'astro';

export async function GET(context: APIContext) {
  let posts: CollectionEntry<'posts'>[] = [];

  try {
    posts = await getCollection('posts', ({ data }) => {
      return !data.draft;
    });
  } catch (error) {
    // Collection doesn't exist or is empty
    posts = [];
  }

  // Sort posts by publish date (newest first)
  const sortedPosts = posts.sort(
    (a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime()
  );

  return rss({
    title: SITE.title,
    description: SITE.description,
    site: context.site || SITE.url,
    items: sortedPosts.map((post) => ({
      title: post.data.title,
      description: post.data.description,
      pubDate: post.data.publishDate,
      link: `/blog/${post.id}/`,
      categories: [post.data.category, ...post.data.tags],
      author: post.data.author || SITE.author,
      customData: `
        <content:encoded><![CDATA[${post.data.description}]]></content:encoded>
        <dc:creator>${post.data.author || SITE.author}</dc:creator>
        ${post.data.coverImage ? `<media:thumbnail url="${post.data.coverImage}" />` : ''}
      `,
    })),
    customData: `
      <language>en-us</language>
      <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
      <generator>Astro v${process.env.npm_package_version || '5.0.0'}</generator>
      <webMaster>${SITE.social.email} (${SITE.author})</webMaster>
      <managingEditor>${SITE.social.email} (${SITE.author})</managingEditor>
      <category>Technology</category>
      <category>AI Tools</category>
      <category>Content Creation</category>
      <ttl>60</ttl>
      <image>
        <url>${SITE.url}/og-default.png</url>
        <title>${SITE.title}</title>
        <link>${SITE.url}</link>
        <width>1200</width>
        <height>630</height>
      </image>
    `,
    xmlns: {
      content: 'http://purl.org/rss/1.0/modules/content/',
      dc: 'http://purl.org/dc/elements/1.1/',
      media: 'http://search.yahoo.com/mrss/',
    },
    stylesheet: '/rss-styles.xsl',
  });
}