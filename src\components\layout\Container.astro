---
export interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  class?: string;
  as?: keyof HTMLElementTagNameMap;
}

const { 
  size = 'lg', 
  class: className = '',
  as: Element = 'div'
} = Astro.props;

const sizeClasses = {
  sm: 'max-w-2xl',
  md: 'max-w-4xl', 
  lg: 'max-w-6xl',
  xl: 'max-w-7xl',
  full: 'max-w-full'
};
---

<Element 
  class={`mx-auto w-full px-4 sm:px-6 lg:px-8 ${sizeClasses[size]} ${className}`}
  {...Astro.props}
>
  <slot />
</Element>