---
import BaseLayout from '../layouts/BaseLayout.astro';
import Container from '../components/layout/Container.astro';
import GradientSection from '../components/layout/GradientSection.astro';

const seo = {
  title: 'Offline - CreatorAITools',
  description: 'You are currently offline. Please check your internet connection.',
  noindex: true,
};
---

<BaseLayout title={seo.title} description={seo.description} noindex={seo.noindex}>
  <GradientSection class="min-h-screen flex items-center">
    <Container>
      <div class="text-center max-w-2xl mx-auto">
        <!-- Offline Icon -->
        <div class="mb-8">
          <div class="w-24 h-24 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <svg
              class="w-12 h-12 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="1.5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M18 12L6 12M6 12l6-6M6 12l6 6"
              />
            </svg>
          </div>
        </div>

        <!-- Heading -->
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
          You're <span class="gradient-text">Offline</span>
        </h1>

        <!-- Description -->
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
          It looks like you've lost your internet connection. Don't worry - some content may still be available from your cache.
        </p>

        <!-- Actions -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <button
            onclick="window.location.reload()"
            class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            <svg
              class="w-5 h-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Try Again
          </button>

          <a
            href="/"
            class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            <svg
              class="w-5 h-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Go Home
          </a>
        </div>

        <!-- Connection Status -->
        <div class="mt-12 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center justify-center space-x-2">
            <div id="connection-status" class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                Connection Status: <span id="status-text">Offline</span>
              </span>
            </div>
          </div>
        </div>

        <!-- Cached Content -->
        <div class="mt-8 text-left">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Available Offline Content
          </h2>
          <div id="cached-content" class="space-y-2">
            <p class="text-gray-600 dark:text-gray-400">
              Loading cached content...
            </p>
          </div>
        </div>
      </div>
    </Container>
  </GradientSection>
</BaseLayout>

<script>
  // Monitor connection status
  function updateConnectionStatus() {
    const statusIndicator = document.querySelector('#connection-status .w-3');
    const statusText = document.getElementById('status-text');
    
    if (navigator.onLine) {
      statusIndicator?.classList.remove('bg-red-500', 'animate-pulse');
      statusIndicator?.classList.add('bg-green-500');
      if (statusText) statusText.textContent = 'Online';
    } else {
      statusIndicator?.classList.remove('bg-green-500');
      statusIndicator?.classList.add('bg-red-500', 'animate-pulse');
      if (statusText) statusText.textContent = 'Offline';
    }
  }

  // Load cached content
  async function loadCachedContent() {
    const cachedContentEl = document.getElementById('cached-content');
    if (!cachedContentEl) return;

    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        const cachedUrls = new Set();

        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const requests = await cache.keys();
          
          requests.forEach(request => {
            const url = new URL(request.url);
            if (url.origin === location.origin && url.pathname !== '/offline') {
              cachedUrls.add(url.pathname);
            }
          });
        }

        if (cachedUrls.size > 0) {
          const urlList = Array.from(cachedUrls)
            .filter(url => url !== '/offline')
            .slice(0, 10) // Show max 10 items
            .map(url => {
              const title = url === '/' ? 'Home' : 
                           url.startsWith('/blog/') ? 'Blog Post' :
                           url.replace(/^\//, '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              
              return `
                <a href="${url}" class="block p-3 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                  <div class="font-medium text-gray-900 dark:text-white">${title}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">${url}</div>
                </a>
              `;
            }).join('');

          cachedContentEl.innerHTML = urlList || '<p class="text-gray-600 dark:text-gray-400">No cached content available.</p>';
        } else {
          cachedContentEl.innerHTML = '<p class="text-gray-600 dark:text-gray-400">No cached content available.</p>';
        }
      } else {
        cachedContentEl.innerHTML = '<p class="text-gray-600 dark:text-gray-400">Cache not supported in this browser.</p>';
      }
    } catch (error) {
      console.error('Error loading cached content:', error);
      cachedContentEl.innerHTML = '<p class="text-gray-600 dark:text-gray-400">Error loading cached content.</p>';
    }
  }

  // Initialize
  document.addEventListener('DOMContentLoaded', () => {
    updateConnectionStatus();
    loadCachedContent();

    // Listen for connection changes
    window.addEventListener('online', updateConnectionStatus);
    window.addEventListener('offline', updateConnectionStatus);

    // Auto-reload when back online
    window.addEventListener('online', () => {
      setTimeout(() => {
        if (confirm('Connection restored! Would you like to reload the page?')) {
          window.location.reload();
        }
      }, 1000);
    });
  });
</script>

<style>
  /* Ensure proper spacing on mobile */
  @media (max-width: 640px) {
    .space-y-4 > * + * {
      margin-top: 1rem;
    }
  }
</style>