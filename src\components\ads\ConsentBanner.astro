---
import { ADS_CONFIG } from '../../config/ads';

// Only show consent banner if ads are enabled and consent is required
const showBanner = ADS_CONFIG.enabled && ADS_CONFIG.privacy.requireConsent;
---

{showBanner && (
  <div
    id="consent-banner"
    class="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg transform translate-y-full transition-transform duration-300"
    role="dialog"
    aria-labelledby="consent-title"
    aria-describedby="consent-description"
  >
    <div class="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
      <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div class="flex-1">
          <h3 id="consent-title" class="text-sm font-medium text-gray-900 dark:text-white mb-1">
            <PERSON><PERSON>
          </h3>
          <p id="consent-description" class="text-sm text-gray-600 dark:text-gray-400">
            We use cookies to improve your experience and show relevant ads. By continuing to use our site, you agree to our use of cookies.
            <a href="/privacy" class="text-indigo-600 dark:text-indigo-400 hover:underline ml-1">
              Learn more
            </a>
          </p>
        </div>
        
        <div class="flex items-center gap-3">
          <button
            id="consent-decline"
            class="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Decline
          </button>
          <button
            id="consent-accept"
            class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Accept
          </button>
        </div>
      </div>
    </div>
  </div>
)}

<script is:inline define:vars={{ showBanner }}>
  if (showBanner) {
    document.addEventListener('DOMContentLoaded', function() {
      const banner = document.getElementById('consent-banner');
      const acceptBtn = document.getElementById('consent-accept');
      const declineBtn = document.getElementById('consent-decline');
      
      // Check if consent has already been given
      const hasConsent = localStorage.getItem('cookie-consent');
      
      if (!hasConsent) {
        // Show banner after a short delay
        setTimeout(() => {
          if (banner) {
            banner.classList.remove('translate-y-full');
          }
        }, 1000);
      }
      
      // Handle accept
      acceptBtn?.addEventListener('click', () => {
        localStorage.setItem('cookie-consent', 'accepted');
        hideBanner();
        
        // Enable ads if consent is given
        if (window.enableAds) {
          window.enableAds();
        }
      });
      
      // Handle decline
      declineBtn?.addEventListener('click', () => {
        localStorage.setItem('cookie-consent', 'declined');
        hideBanner();
      });
      
      function hideBanner() {
        if (banner) {
          banner.classList.add('translate-y-full');
          setTimeout(() => {
            banner.style.display = 'none';
          }, 300);
        }
      }
    });
  }
</script>

<style>
  /* Ensure banner is above other content */
  #consent-banner {
    z-index: 9999;
  }
  
  /* Mobile optimizations */
  @media (max-width: 640px) {
    #consent-banner {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
</style>