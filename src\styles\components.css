/* Component-specific styles */

/* Button component enhancements */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* Card component hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text animation */
.gradient-text-animated {
  background: linear-gradient(-45deg, #6366f1, #8b5cf6, #d946ef, #6366f1);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Focus indicators */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scroll indicators */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #6366f1, #8b5cf6, #d946ef);
  transform-origin: left;
  z-index: 1000;
}

/* Mobile-specific component styles */
@media (max-width: 768px) {
  /* Mobile card spacing */
  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  /* Mobile button groups */
  .button-group {
    flex-direction: column;
    width: 100%;
  }
  
  .button-group > * {
    width: 100%;
  }
  
  /* Mobile navigation */
  .mobile-nav-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .dark .mobile-nav-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  /* Mobile hero adjustments */
  .hero-mobile {
    padding-top: 2rem;
    padding-bottom: 2rem;
    text-align: center;
  }
  
  /* Mobile form elements */
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    width: 100%;
    padding: 0.75rem;
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes for mobile */
.mobile-center {
  @media (max-width: 768px) {
    text-align: center;
  }
}

.mobile-hidden {
  @media (max-width: 768px) {
    display: none;
  }
}

.mobile-visible {
  display: none;
  @media (max-width: 768px) {
    display: block;
  }
}