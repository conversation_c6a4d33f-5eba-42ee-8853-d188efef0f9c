---
export interface Props {
  href?: string;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  class?: string;
  disabled?: boolean;
  target?: '_blank' | '_self' | '_parent' | '_top';
  rel?: string;
}

const {
  href,
  type = 'button',
  variant = 'primary',
  size = 'md',
  class: className = '',
  disabled = false,
  target,
  rel,
} = Astro.props;

const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 active:scale-95 transform';

const variantClasses = {
  primary: 'bg-gradient-to-r from-indigo-500 to-violet-500 hover:from-indigo-600 hover:to-violet-600 text-white focus:ring-indigo-500 shadow-md hover:shadow-lg',
  secondary: 'bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-indigo-500',
  outline: 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-indigo-500',
  ghost: 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-indigo-500'
};

const sizeClasses = {
  sm: 'px-3 py-2 text-sm min-h-[36px]',
  md: 'px-4 py-2.5 text-sm min-h-[40px]',
  lg: 'px-6 py-3 text-base min-h-[44px]'
};

const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : '';

const allClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;

const Element = href ? 'a' : 'button';
---

<Element
  class={allClasses}
  href={href}
  type={href ? undefined : type}
  disabled={href ? undefined : disabled}
  target={target}
  rel={target === '_blank' ? 'noopener noreferrer' : rel}
>
  <slot />
</Element>

<style>
  /* Ensure proper touch targets on mobile */
  @media (max-width: 768px) {
    button,
    a {
      min-height: 44px;
      min-width: 44px;
    }
  }
</style>