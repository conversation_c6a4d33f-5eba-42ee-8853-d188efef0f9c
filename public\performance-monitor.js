// Core Web Vitals monitoring for CreatorAITools
// This script monitors and reports performance metrics

(function () {
    'use strict';

    // Check if we're in a browser environment
    if (typeof window === 'undefined') return;

    // Performance metrics storage
    const metrics = {
        lcp: null,
        fid: null,
        cls: null,
        fcp: null,
        ttfb: null,
        inp: null
    };

    // Utility function to send metrics
    function sendMetric(name, value, rating) {
        // Log to console in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log(`${name}: ${value}ms (${rating})`);
        }

        // Send to analytics (replace with your analytics service)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'web_vitals', {
                event_category: 'Performance',
                event_label: name,
                value: Math.round(value),
                custom_map: {
                    metric_rating: rating,
                    page_url: window.location.href
                }
            });
        }

        // Store in localStorage for debugging
        try {
            const perfData = JSON.parse(localStorage.getItem('creatoraitools_perf') || '{}');
            perfData[name] = {
                value: value,
                rating: rating,
                timestamp: Date.now(),
                url: window.location.pathname
            };
            localStorage.setItem('creatoraitools_perf', JSON.stringify(perfData));
        } catch (e) {
            // Ignore localStorage errors
        }
    }

    // Rating thresholds
    const thresholds = {
        lcp: { good: 2500, poor: 4000 },
        fid: { good: 100, poor: 300 },
        cls: { good: 0.1, poor: 0.25 },
        fcp: { good: 1800, poor: 3000 },
        ttfb: { good: 800, poor: 1800 },
        inp: { good: 200, poor: 500 }
    };

    function getRating(metric, value) {
        const threshold = thresholds[metric];
        if (!threshold) return 'unknown';
        if (value <= threshold.good) return 'good';
        if (value <= threshold.poor) return 'needs-improvement';
        return 'poor';
    }

    // Largest Contentful Paint (LCP)
    function measureLCP() {
        if (!('PerformanceObserver' in window)) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];

                if (lastEntry) {
                    metrics.lcp = lastEntry.startTime;
                    sendMetric('LCP', lastEntry.startTime, getRating('lcp', lastEntry.startTime));
                }
            });

            observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
            console.warn('LCP measurement failed:', e);
        }
    }

    // First Input Delay (FID)
    function measureFID() {
        if (!('PerformanceObserver' in window)) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();

                entries.forEach((entry) => {
                    const fid = entry.processingStart - entry.startTime;
                    metrics.fid = fid;
                    sendMetric('FID', fid, getRating('fid', fid));
                });
            });

            observer.observe({ entryTypes: ['first-input'] });
        } catch (e) {
            console.warn('FID measurement failed:', e);
        }
    }

    // Cumulative Layout Shift (CLS)
    function measureCLS() {
        if (!('PerformanceObserver' in window)) return;

        try {
            let clsValue = 0;
            let sessionValue = 0;
            let sessionEntries = [];

            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();

                entries.forEach((entry) => {
                    if (!entry.hadRecentInput) {
                        const firstSessionEntry = sessionEntries[0];
                        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

                        if (sessionValue &&
                            entry.startTime - lastSessionEntry.startTime < 1000 &&
                            entry.startTime - firstSessionEntry.startTime < 5000) {
                            sessionValue += entry.value;
                            sessionEntries.push(entry);
                        } else {
                            sessionValue = entry.value;
                            sessionEntries = [entry];
                        }

                        if (sessionValue > clsValue) {
                            clsValue = sessionValue;
                            metrics.cls = clsValue;
                            sendMetric('CLS', clsValue, getRating('cls', clsValue));
                        }
                    }
                });
            });

            observer.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
            console.warn('CLS measurement failed:', e);
        }
    }

    // First Contentful Paint (FCP)
    function measureFCP() {
        if (!('PerformanceObserver' in window)) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();

                entries.forEach((entry) => {
                    if (entry.name === 'first-contentful-paint') {
                        metrics.fcp = entry.startTime;
                        sendMetric('FCP', entry.startTime, getRating('fcp', entry.startTime));
                    }
                });
            });

            observer.observe({ entryTypes: ['paint'] });
        } catch (e) {
            console.warn('FCP measurement failed:', e);
        }
    }

    // Time to First Byte (TTFB)
    function measureTTFB() {
        try {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                const ttfb = navigation.responseStart - navigation.requestStart;
                metrics.ttfb = ttfb;
                sendMetric('TTFB', ttfb, getRating('ttfb', ttfb));
            }
        } catch (e) {
            console.warn('TTFB measurement failed:', e);
        }
    }

    // Interaction to Next Paint (INP) - experimental
    function measureINP() {
        if (!('PerformanceObserver' in window)) return;

        try {
            let interactions = [];

            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();

                entries.forEach((entry) => {
                    interactions.push(entry.duration);

                    // Keep only the worst 10 interactions
                    interactions.sort((a, b) => b - a);
                    if (interactions.length > 10) {
                        interactions = interactions.slice(0, 10);
                    }

                    // Calculate INP as 98th percentile
                    const inp = interactions[Math.min(Math.floor(interactions.length * 0.98), interactions.length - 1)];

                    if (inp !== undefined) {
                        metrics.inp = inp;
                        sendMetric('INP', inp, getRating('inp', inp));
                    }
                });
            });

            observer.observe({ entryTypes: ['event'] });
        } catch (e) {
            console.warn('INP measurement failed:', e);
        }
    }

    // Mobile-specific metrics
    function measureMobileMetrics() {
        // Device memory
        if ('deviceMemory' in navigator) {
            sendMetric('Device_Memory', navigator.deviceMemory,
                navigator.deviceMemory >= 4 ? 'good' :
                    navigator.deviceMemory >= 2 ? 'needs-improvement' : 'poor');
        }

        // Connection type
        if ('connection' in navigator) {
            const connection = navigator.connection;
            sendMetric('Connection_Type', connection.effectiveType,
                connection.effectiveType === '4g' ? 'good' :
                    connection.effectiveType === '3g' ? 'needs-improvement' : 'poor');

            if (connection.downlink) {
                sendMetric('Connection_Speed', connection.downlink,
                    connection.downlink >= 10 ? 'good' :
                        connection.downlink >= 1.5 ? 'needs-improvement' : 'poor');
            }
        }

        // Battery status
        if ('getBattery' in navigator) {
            navigator.getBattery().then((battery) => {
                sendMetric('Battery_Level', battery.level * 100,
                    battery.level > 0.5 ? 'good' :
                        battery.level > 0.2 ? 'needs-improvement' : 'poor');
            });
        }
    }

    // Resource timing
    function measureResourceTiming() {
        try {
            const resources = performance.getEntriesByType('resource');
            const imageResources = resources.filter(r => r.initiatorType === 'img');
            const cssResources = resources.filter(r => r.initiatorType === 'css');
            const jsResources = resources.filter(r => r.initiatorType === 'script');

            if (imageResources.length > 0) {
                const avgImageLoad = imageResources.reduce((sum, r) => sum + r.duration, 0) / imageResources.length;
                sendMetric('Avg_Image_Load', avgImageLoad,
                    avgImageLoad < 500 ? 'good' :
                        avgImageLoad < 1000 ? 'needs-improvement' : 'poor');
            }

            if (cssResources.length > 0) {
                const avgCSSLoad = cssResources.reduce((sum, r) => sum + r.duration, 0) / cssResources.length;
                sendMetric('Avg_CSS_Load', avgCSSLoad,
                    avgCSSLoad < 200 ? 'good' :
                        avgCSSLoad < 500 ? 'needs-improvement' : 'poor');
            }

            if (jsResources.length > 0) {
                const avgJSLoad = jsResources.reduce((sum, r) => sum + r.duration, 0) / jsResources.length;
                sendMetric('Avg_JS_Load', avgJSLoad,
                    avgJSLoad < 300 ? 'good' :
                        avgJSLoad < 600 ? 'needs-improvement' : 'poor');
            }
        } catch (e) {
            console.warn('Resource timing measurement failed:', e);
        }
    }

    // Initialize measurements
    function init() {
        // Wait for page to be fully loaded
        if (document.readyState === 'complete') {
            startMeasurements();
        } else {
            window.addEventListener('load', startMeasurements);
        }
    }

    function startMeasurements() {
        measureLCP();
        measureFID();
        measureCLS();
        measureFCP();
        measureTTFB();
        measureINP();
        measureMobileMetrics();

        // Measure resource timing after a delay
        setTimeout(measureResourceTiming, 2000);
    }

    // Export metrics for debugging
    window.CreatorAIToolsPerf = {
        getMetrics: () => metrics,
        clearMetrics: () => {
            Object.keys(metrics).forEach(key => metrics[key] = null);
            localStorage.removeItem('creatoraitools_perf');
        },
        exportMetrics: () => {
            const perfData = localStorage.getItem('creatoraitools_perf');
            if (perfData) {
                const blob = new Blob([perfData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `creatoraitools-perf-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        }
    };

    // Start monitoring
    init();

    // Report final metrics before page unload
    window.addEventListener('beforeunload', () => {
        // Send any remaining metrics
        Object.keys(metrics).forEach(key => {
            if (metrics[key] !== null) {
                sendMetric(key, metrics[key], getRating(key, metrics[key]));
            }
        });
    });

})();