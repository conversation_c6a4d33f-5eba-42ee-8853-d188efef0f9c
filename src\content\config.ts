import { defineCollection, z } from 'astro:content';

const postsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    publishDate: z.date(),
    updatedDate: z.date().optional(),
    category: z.enum(['AI Writing', 'Thumbnails', 'Video Automation', 'SEO for Creators']),
    tags: z.array(z.string()),
    coverImage: z.string().optional(),
    draft: z.boolean().default(false),
    author: z.string().optional(),
    readingTime: z.number().optional(),
    featured: z.boolean().default(false),
    difficulty: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  }),
});

export const collections = {
  posts: postsCollection,
};
