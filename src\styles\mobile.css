/* Mobile-first CSS optimizations for CreatorAITools */

/* Touch and interaction improvements */
@media (max-width: 768px) {
  /* Improve touch targets */
  button,
  a,
  input,
  select,
  textarea,
  [role="button"],
  [tabindex="0"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better tap highlighting */
  * {
    -webkit-tap-highlight-color: rgba(99, 102, 241, 0.2);
    tap-highlight-color: rgba(99, 102, 241, 0.2);
  }

  /* Prevent zoom on form inputs */
  input[type="text"],
  input[type="email"],
  input[type="search"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    transform: translateZ(0); /* Force hardware acceleration */
  }

  /* Optimize scrolling */
  body,
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
  }

  /* Better text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Improve button spacing and sizing */
  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-group button,
  .btn-group a {
    width: 100%;
    justify-content: center;
  }

  /* Card optimizations */
  .card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Typography adjustments */
  h1 {
    font-size: 2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  h3 {
    font-size: 1.25rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    max-width: 65ch;
  }

  /* List improvements */
  ul,
  ol {
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  /* Table responsiveness */
  table {
    width: 100%;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
  }

  thead,
  tbody,
  tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  /* Form improvements */
  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  input,
  textarea,
  select {
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    background-color: white;
    transition: border-color 0.2s ease;
  }

  input:focus,
  textarea:focus,
  select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  /* Navigation improvements */
  .nav-mobile {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
  }

  .nav-mobile.dark {
    background: rgba(17, 24, 39, 0.95);
    border-bottom-color: #374151;
  }

  /* Modal and overlay improvements */
  .modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    backdrop-filter: blur(4px);
  }

  .modal-content {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .modal-content.open {
    transform: translateY(0);
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(
      90deg,
      #f3f4f6 25%,
      #e5e7eb 50%,
      #f3f4f6 75%
    );
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* Pull-to-refresh indicator */
  .pull-to-refresh {
    position: fixed;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 2rem;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: top 0.3s ease;
    z-index: 60;
  }

  .pull-to-refresh.active {
    top: 20px;
  }

  /* Swipe gestures */
  .swipeable {
    touch-action: pan-y;
    user-select: none;
  }

  /* Floating elements */
  .floating-element {
    position: fixed;
    z-index: 30;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }

  .floating-element:active {
    transform: scale(0.95);
  }

  /* Toast notifications */
  .toast-container {
    position: fixed;
    top: 1rem;
    left: 1rem;
    right: 1rem;
    z-index: 60;
    pointer-events: none;
  }

  .toast {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #6366f1;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
  }

  .toast.show {
    transform: translateX(0);
  }

  /* Progress indicators */
  .progress-bar {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  /* Sticky elements */
  .sticky-mobile {
    position: sticky;
    top: 0;
    z-index: 20;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  /* Safe area handling for notched devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Landscape orientation adjustments */
  @media (orientation: landscape) and (max-height: 500px) {
    .modal-content {
      max-height: 80vh;
    }

    .nav-mobile {
      padding: 0.5rem 1rem;
    }

    h1 {
      font-size: 1.75rem;
    }

    h2 {
      font-size: 1.25rem;
    }
  }
}

/* Very small screens (< 375px) */
@media (max-width: 374px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.25rem;
  }

  .btn-group {
    gap: 0.5rem;
  }

  .card {
    padding: 1rem;
  }
}

/* Large phones and small tablets (375px - 768px) */
@media (min-width: 375px) and (max-width: 768px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .btn-group.horizontal {
    flex-direction: row;
    gap: 1rem;
  }

  .btn-group.horizontal button,
  .btn-group.horizontal a {
    flex: 1;
  }
}

/* Tablet portrait (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .btn-group {
    flex-direction: row;
    gap: 1rem;
  }

  .btn-group button,
  .btn-group a {
    flex: 1;
  }

  /* Tablet-specific touch targets */
  button,
  a,
  input,
  select,
  textarea {
    min-height: 40px;
    min-width: 40px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders and shadows */
  .card,
  .modal-content,
  .toast {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Better image rendering */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) {
  @media (max-width: 768px) {
    .nav-mobile {
      background: rgba(17, 24, 39, 0.95);
      border-bottom-color: #374151;
    }

    .modal-content {
      background: #1f2937;
      color: #f9fafb;
    }

    .toast {
      background: #1f2937;
      color: #f9fafb;
      border-left-color: #8b5cf6;
    }

    .loading-skeleton {
      background: linear-gradient(
        90deg,
        #374151 25%,
        #4b5563 50%,
        #374151 75%
      );
    }

    input,
    textarea,
    select {
      background-color: #1f2937;
      border-color: #4b5563;
      color: #f9fafb;
    }

    input:focus,
    textarea:focus,
    select:focus {
      border-color: #8b5cf6;
      box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    }
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .loading-skeleton {
    animation: none;
    background: #f3f4f6;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  button,
  input,
  textarea,
  select {
    border-width: 2px;
  }

  .card {
    border-width: 2px;
  }

  .toast {
    border-width: 2px;
  }
}

/* Print styles for mobile */
@media print {
  .nav-mobile,
  .floating-element,
  .toast-container,
  .modal-overlay,
  .pull-to-refresh {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}