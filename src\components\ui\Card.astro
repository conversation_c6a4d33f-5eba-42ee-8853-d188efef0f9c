---
export interface Props {
  class?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  as?: keyof HTMLElementTagNameMap;
}

const {
  class: className = '',
  padding = 'md',
  hover = false,
  as: Element = 'div'
} = Astro.props;

const paddingClasses = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8'
};

const baseClasses = 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-soft';
const hoverClasses = hover ? 'hover:shadow-soft-lg hover:-translate-y-1 transition-all duration-200' : '';
---

<Element class={`${baseClasses} ${hoverClasses} ${paddingClasses[padding]} ${className}`}>
  <slot />
</Element>