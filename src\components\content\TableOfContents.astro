---
import type { MarkdownHeading } from 'astro';

export interface Props {
  headings: MarkdownHeading[];
  mobile?: boolean;
  class?: string;
}

const { headings, mobile = false, class: className } = Astro.props;

// Filter headings to only include h2 and h3 for better readability
const tocHeadings = headings.filter(heading => heading.depth <= 3 && heading.depth >= 2);

// Generate nested structure for better display
function buildTocTree(headings: MarkdownHeading[]) {
  const tree: Array<MarkdownHeading & { children?: MarkdownHeading[] }> = [];
  const stack: Array<MarkdownHeading & { children?: MarkdownHeading[] }> = [];

  for (const heading of headings) {
    const item = { ...heading, children: [] };

    // Find the correct parent level
    while (stack.length > 0 && stack[stack.length - 1].depth >= heading.depth) {
      stack.pop();
    }

    if (stack.length === 0) {
      tree.push(item);
    } else {
      const parent = stack[stack.length - 1];
      if (!parent.children) parent.children = [];
      parent.children.push(item);
    }

    stack.push(item);
  }

  return tree;
}

const tocTree = buildTocTree(tocHeadings);
---

{tocHeadings.length > 0 && (
  <nav class={`toc ${className || ''}`} aria-label="Table of Contents">
    <div class="toc-header">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <span class="mr-2">📋</span>
        Table of Contents
      </h2>
    </div>
    
    <div id="toc-content" class="toc-content">
      <ul class="space-y-2 text-sm">
        {tocTree.map((heading) => (
          <li>
            <a
              href={`#${heading.slug}`}
              class="block py-1 text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors border-l-2 border-transparent hover:border-primary-300 pl-3 -ml-3"
              data-toc-link
              data-depth={heading.depth}
            >
              {heading.text}
            </a>
            {heading.children && heading.children.length > 0 && (
              <ul class="mt-1 ml-4 space-y-1">
                {heading.children.map((child) => (
                  <li>
                    <a
                      href={`#${child.slug}`}
                      class="block py-1 text-gray-500 hover:text-primary-600 dark:text-gray-500 dark:hover:text-primary-400 transition-colors text-xs border-l-2 border-transparent hover:border-primary-200 pl-2 -ml-2"
                      data-toc-link
                      data-depth={child.depth}
                    >
                      {child.text}
                    </a>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </div>

    <!-- Mobile Toggle Button -->
    <button
      id="toc-toggle"
      class="lg:hidden w-full mt-4 px-4 py-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 active:scale-95 min-h-[44px]"
      aria-expanded="false"
      aria-controls="toc-content"
      aria-label="Toggle table of contents"
    >
      <span class="flex items-center justify-center">
        <svg class="w-4 h-4 mr-2 transition-transform duration-200" id="toc-chevron" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
        <span id="toc-toggle-text">Show Contents</span>
      </span>
    </button>

    <!-- Reading Progress Bar -->
    <div class="hidden lg:block mt-4">
      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
        <span>Reading Progress</span>
        <span id="reading-percentage" class="ml-auto">0%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
        <div id="reading-progress" class="bg-gradient-to-r from-indigo-500 to-violet-500 h-1.5 rounded-full transition-all duration-300" style="width: 0%"></div>
      </div>
    </div>
  </nav>
)}

<script>
  // Table of Contents functionality
  document.addEventListener('DOMContentLoaded', function() {
    const tocLinks = document.querySelectorAll('[data-toc-link]');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const tocToggle = document.getElementById('toc-toggle');
    const tocContent = document.getElementById('toc-content');
    const tocChevron = document.getElementById('toc-chevron');
    const tocToggleText = document.getElementById('toc-toggle-text');
    const readingProgress = document.getElementById('reading-progress');
    const readingPercentage = document.getElementById('reading-percentage');
    
    if (tocLinks.length === 0 || headings.length === 0) return;

    // Mobile TOC toggle functionality
    let isTocOpen = false;
    
    function toggleToc() {
      isTocOpen = !isTocOpen;
      
      if (isTocOpen) {
        tocContent?.classList.remove('hidden');
        tocChevron?.classList.add('rotate-180');
        tocToggle?.setAttribute('aria-expanded', 'true');
        if (tocToggleText) tocToggleText.textContent = 'Hide Contents';
      } else {
        tocContent?.classList.add('hidden');
        tocChevron?.classList.remove('rotate-180');
        tocToggle?.setAttribute('aria-expanded', 'false');
        if (tocToggleText) tocToggleText.textContent = 'Show Contents';
      }
      
      // Haptic feedback on mobile
      if ('vibrate' in navigator) {
        navigator.vibrate(25);
      }
    }

    tocToggle?.addEventListener('click', toggleToc);

    // Auto-hide mobile TOC content initially
    const isMobile = window.innerWidth < 1024;
    if (isMobile && tocContent) {
      tocContent.classList.add('hidden');
    }

    // Reading progress calculation
    function updateReadingProgress() {
      if (!readingProgress || !readingPercentage) return;
      
      const article = document.querySelector('article') || document.querySelector('main');
      if (!article) return;
      
      const articleTop = article.offsetTop;
      const articleHeight = article.offsetHeight;
      const windowHeight = window.innerHeight;
      const scrollTop = window.pageYOffset;
      
      const articleBottom = articleTop + articleHeight;
      const windowBottom = scrollTop + windowHeight;
      
      let progress = 0;
      
      if (scrollTop >= articleTop) {
        if (windowBottom >= articleBottom) {
          progress = 100;
        } else {
          const visibleHeight = windowBottom - articleTop;
          progress = (visibleHeight / articleHeight) * 100;
        }
      }
      
      progress = Math.min(100, Math.max(0, progress));
      
      readingProgress.style.width = `${progress}%`;
      readingPercentage.textContent = `${Math.round(progress)}%`;
    }

    // Create intersection observer for active link highlighting
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const id = entry.target.getAttribute('id');
          const tocLink = document.querySelector(`[data-toc-link][href="#${id}"]`);
          
          if (entry.isIntersecting) {
            // Remove active class from all links
            tocLinks.forEach(link => {
              link.classList.remove('text-primary-600', 'dark:text-primary-400', 'border-primary-500', 'font-medium');
              link.classList.add('text-gray-600', 'dark:text-gray-400', 'border-transparent');
            });
            
            // Add active class to current link
            if (tocLink) {
              tocLink.classList.remove('text-gray-600', 'dark:text-gray-400', 'border-transparent');
              tocLink.classList.add('text-primary-600', 'dark:text-primary-400', 'border-primary-500', 'font-medium');
            }
          }
        });
      },
      {
        rootMargin: '-20% 0% -35% 0%',
        threshold: 0
      }
    );

    // Observe all headings
    headings.forEach((heading) => {
      if (heading.getAttribute('id')) {
        observer.observe(heading);
      }
    });

    // Smooth scroll for TOC links
    tocLinks.forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href')?.substring(1);
        const targetElement = document.getElementById(targetId || '');
        
        if (targetElement) {
          const headerOffset = 100; // Account for sticky header
          const elementPosition = targetElement.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });

          // Update URL without triggering scroll
          history.pushState(null, '', `#${targetId}`);
          
          // Auto-close mobile TOC after navigation
          if (isMobile && isTocOpen) {
            setTimeout(() => toggleToc(), 300);
          }
          
          // Haptic feedback
          if ('vibrate' in navigator) {
            navigator.vibrate(50);
          }
        }
      });
    });

    // Update reading progress on scroll
    window.addEventListener('scroll', updateReadingProgress);
    window.addEventListener('resize', updateReadingProgress);
    
    // Initial progress calculation
    updateReadingProgress();

    // Handle window resize for mobile/desktop switching
    window.addEventListener('resize', () => {
      const newIsMobile = window.innerWidth < 1024;
      if (!newIsMobile && tocContent) {
        // Show TOC on desktop
        tocContent.classList.remove('hidden');
        isTocOpen = true;
      } else if (newIsMobile && tocContent && !isTocOpen) {
        // Hide TOC on mobile if not explicitly opened
        tocContent.classList.add('hidden');
      }
    });
  });
</script>

<style>
  .toc {
    background-color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    border: 1px solid rgb(229 231 235);
  }

  [data-theme="dark"] .toc {
    background-color: rgb(31 41 55);
    border-color: rgb(55 65 81);
  }

  @media (max-width: 1023px) {
    .toc {
      margin-bottom: 2rem;
    }
  }

  /* Smooth transitions for TOC links */
  [data-toc-link] {
    transition: all 0.2s ease;
  }

  /* Custom scrollbar for TOC if it gets too long */
  .toc-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .toc-content::-webkit-scrollbar {
    width: 4px;
  }

  .toc-content::-webkit-scrollbar-track {
    background-color: rgb(243 244 246);
    border-radius: 0.25rem;
  }

  [data-theme="dark"] .toc-content::-webkit-scrollbar-track {
    background-color: rgb(55 65 81);
  }

  .toc-content::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.25rem;
  }

  [data-theme="dark"] .toc-content::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
  }

  .toc-content::-webkit-scrollbar-thumb:hover {
    background-color: rgb(156 163 175);
  }

  [data-theme="dark"] .toc-content::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
</style>