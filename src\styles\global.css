@import "tailwindcss";

/* Base styles */
:root {
  --accent: #0ea5e9;
  --accent-dark: #0284c7;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: white !important;
  color: #171717 !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

html.dark body {
  background-color: #0a0a0a !important;
  color: #fafafa !important;
}

.app-bg {
  background-color: white;
}

html.dark .app-bg {
  background-color: #0a0a0a;
}

.surface {
  background-color: white;
  border: 1px solid #e5e5e5;
  border-radius: 1rem;
  padding: 1.25rem;
  transition: all 0.15s ease;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

html.dark .surface {
  background-color: #262626;
  border-color: #404040;
}

.chip {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  border: 1px solid #e5e5e5;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  background-color: #f5f5f5;
  color: #404040;
}

html.dark .chip {
  background-color: #262626;
  color: #e5e5e5;
  border-color: #404040;
}

.input,
.textarea {
  border-radius: 0.75rem;
  border: 1px solid #e5e5e5;
  padding: 0.75rem 1rem;
  width: 100%;
  background-color: #f5f5f5;
  color: #171717;
  transition: all 0.15s ease;
}

.input::placeholder,
.textarea::placeholder {
  color: #a3a3a3;
}

html.dark .input,
html.dark .textarea {
  background-color: #262626;
  border-color: #404040;
  color: #fafafa;
}

html.dark .input::placeholder,
html.dark .textarea::placeholder {
  color: #a3a3a3;
}

.input:focus,
.textarea:focus {
  border-color: #0ea5e9;
  outline: none;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

html.dark .input:focus,
html.dark .textarea:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2);
}

.btn-primary {
  border-radius: 0.75rem;
  padding: 0.75rem 1.25rem;
  background-color: #0ea5e9;
  color: white;
  border: 1px solid #0ea5e9;
  cursor: pointer;
  transition: all 0.15s ease;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #0284c7;
  border-color: #0284c7;
}

.btn-secondary {
  border-radius: 0.75rem;
  padding: 0.75rem 1.25rem;
  background-color: #f5f5f5;
  color: #404040;
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.15s ease;
  font-weight: 500;
}

.btn-secondary:hover {
  background-color: #e5e5e5;
}

html.dark .btn-secondary {
  background-color: #262626;
  color: #e5e5e5;
  border-color: #404040;
}

html.dark .btn-secondary:hover {
  background-color: #404040;
}

/* Typography System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');

:root {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Typography scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */
  --text-7xl: 4.5rem;       /* 72px */
  
  /* Line heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Letter spacing */
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
}

/* Base typography */
body {
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography scale */
.text-xs { font-size: var(--text-xs); line-height: var(--leading-tight); }
.text-sm { font-size: var(--text-sm); line-height: var(--leading-snug); }
.text-base { font-size: var(--text-base); line-height: var(--leading-relaxed); }
.text-lg { font-size: var(--text-lg); line-height: var(--leading-relaxed); }
.text-xl { font-size: var(--text-xl); line-height: var(--leading-normal); }
.text-2xl { font-size: var(--text-2xl); line-height: var(--leading-snug); }
.text-3xl { font-size: var(--text-3xl); line-height: var(--leading-snug); }
.text-4xl { font-size: var(--text-4xl); line-height: var(--leading-tight); }
.text-5xl { font-size: var(--text-5xl); line-height: var(--leading-tight); }
.text-6xl { font-size: var(--text-6xl); line-height: var(--leading-tight); }

/* Font weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* Heading system */
h1, .h1 { 
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  color: #171717;
}

h2, .h2 { 
  font-size: var(--text-3xl);
  font-weight: 700;
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-tight);
  color: #171717;
}

h3, .h3 { 
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: var(--leading-snug);
  color: #171717;
}

h4, .h4 { 
  font-size: var(--text-xl);
  font-weight: 600;
  line-height: var(--leading-normal);
  color: #171717;
}

h5, .h5 { 
  font-size: var(--text-lg);
  font-weight: 600;
  line-height: var(--leading-normal);
  color: #171717;
}

h6, .h6 { 
  font-size: var(--text-base);
  font-weight: 600;
  line-height: var(--leading-normal);
  color: #171717;
}

html.dark h1, html.dark .h1,
html.dark h2, html.dark .h2,
html.dark h3, html.dark .h3,
html.dark h4, html.dark .h4,
html.dark h5, html.dark .h5,
html.dark h6, html.dark .h6 {
  color: #fafafa;
}

/* Responsive typography */
@media (max-width: 768px) {
  h1, .h1 { font-size: var(--text-3xl); }
  h2, .h2 { font-size: var(--text-2xl); }
  h3, .h3 { font-size: var(--text-xl); }
  h4, .h4 { font-size: var(--text-lg); }
}

@media (min-width: 1024px) {
  h1, .h1 { font-size: var(--text-5xl); }
  h2, .h2 { font-size: var(--text-4xl); }
  h3, .h3 { font-size: var(--text-3xl); }
}

/* Enhanced prose styles */
.prose {
  max-width: 65ch;
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
}

.prose h1 { margin: 2.5rem 0 1.5rem; }
.prose h2 { margin: 2rem 0 1rem; }
.prose h3 { margin: 1.5rem 0 0.75rem; }
.prose p { margin: 1.25rem 0; }
.prose ul, .prose ol { margin: 1.25rem 0; }
.prose li { margin: 0.5rem 0; }

/* Text colors */
.text-primary { color: #171717; }
.text-secondary { color: #525252; }
.text-muted { color: #a3a3a3; }
.text-accent { color: #0ea5e9; }

html.dark .text-primary { color: #fafafa; }
html.dark .text-secondary { color: #a3a3a3; }
html.dark .text-muted { color: #737373; }

/* Code typography */
.font-mono { font-family: var(--font-mono); }

/* Utility classes */
.text-balance { text-wrap: balance; }
.text-pretty { text-wrap: pretty; }

/* Focus styles */
.focus-visible:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Reading progress bar */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #0ea5e9, #0284c7);
  width: 0%;
  z-index: 50;
}

/* Code block copy button */
pre {
  position: relative;
}

pre .copy-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
  color: #404040;
  cursor: pointer;
}

pre .copy-btn:hover {
  background: rgba(255,255,255,0.1);
}

pre .copy-btn.copied {
  background: #0ea5e9;
  color: white;
}

html.dark pre .copy-btn {
  color: #e5e5e5;
}

/* Gradient section utility */
.gradient-card {
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(to top right, rgba(14, 165, 233, 0.1), rgba(2, 132, 199, 0.1), transparent);
}

/* Mobile nav utilities */
.mobile-nav-panel {
  transition: transform 0.2s ease, opacity 0.2s ease;
  transform-origin: top right;
  transform: scale(0.98);
}

.mobile-nav-panel.open {
  transform: scale(1);
}

.op-0 {
  opacity: 0;
}

.pe-none {
  pointer-events: none;
}

/* Color scheme */
html.dark {
  color-scheme: dark;
}