---
export interface Props {
  class?: string;
  as?: keyof HTMLElementTagNameMap;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

const { 
  class: className = '',
  as: Element = 'section',
  padding = 'lg'
} = Astro.props;

const paddingClasses = {
  none: '',
  sm: 'py-8 sm:py-12',
  md: 'py-12 sm:py-16',
  lg: 'py-16 sm:py-20',
  xl: 'py-20 sm:py-24'
};
---

<Element class={`${paddingClasses[padding]} ${className}`}>
  <slot />
</Element>