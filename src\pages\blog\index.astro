---
import { getCollection, type CollectionEntry } from 'astro:content';
import PageLayout from '../../layouts/PageLayout.astro';
import Container from '../../components/layout/Container.astro';
import Section from '../../components/layout/Section.astro';
import PostCard from '../../components/content/PostCard.astro';
import Button from '../../components/ui/Button.astro';
import { SITE } from '../../config/site';
import { generateSEO, generateStructuredData } from '../../config/seo';

// Get all published posts
let allPosts: CollectionEntry<'posts'>[] = [];
try {
  allPosts = await getCollection('posts', ({ data }) => {
    return !data.draft;
  });
} catch (error) {
  // Collection doesn't exist or is empty
  allPosts = [];
}

// Sort posts by publish date (newest first)
const posts = allPosts.sort(
  (a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime()
);

const seo = {
  title: 'Blog',
  description: 'Discover the latest AI tools, techniques, and strategies for content creators. Learn how to leverage AI for writing, thumbnails, video automation, and SEO.',
  canonical: '/blog',
};

// Generate blog structured data
const blogStructuredData = generateStructuredData(
  { title: seo.title, description: seo.description, canonical: seo.canonical },
  'blog'
);
---

<PageLayout {...seo}>
  <!-- Page Header -->
  <Section class="bg-gradient-to-br from-primary-50 to-violet-50 dark:from-gray-900 dark:to-gray-800">
    <Container>
      <div class="mx-auto max-w-3xl text-center py-12">
        <h1 class="text-fluid-4xl font-bold text-gray-900 dark:text-white mb-6">
          AI Tools & Strategies Blog
        </h1>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 mb-8">
          Discover practical AI tools and techniques to supercharge your content creation workflow.
          From writing assistance to video automation, we cover it all.
        </p>
        
        <!-- Category Filter Chips -->
        <div class="flex flex-wrap justify-center gap-3">
          <Button
            href="/blog"
            variant="outline"
            size="sm"
            class="bg-white/80 backdrop-blur-sm border-primary-200 text-primary-700 hover:bg-primary-50"
          >
            All Posts
          </Button>
          {
            SITE.categories.map((category) => {
              const slug = category.toLowerCase().replace(/\s+/g, '-');
              return (
                <Button
                  href={`/tags/${slug}`}
                  variant="ghost"
                  size="sm"
                  class="text-gray-600 hover:text-primary-600 hover:bg-white/50 backdrop-blur-sm"
                >
                  {category}
                </Button>
              );
            })
          }
        </div>
      </div>
    </Container>
  </Section>

  <!-- Blog Posts -->
  <Section>
    <Container>
      {
        posts.length > 0 ? (
          <>
            <!-- Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <PostCard
                  title={post.data.title}
                  description={post.data.description}
                  slug={post.id}
                  publishDate={post.data.publishDate}
                  tags={post.data.tags}
                  category={post.data.category}
                  coverImage={post.data.coverImage}
                  readingTime={post.data.readingTime}
                />
              ))}
            </div>

            <!-- Pagination (placeholder for future implementation) -->
            <div class="mt-16 flex justify-center">
              <nav class="flex items-center space-x-2" aria-label="Pagination">
                <Button variant="outline" size="sm" disabled>
                  ← Previous
                </Button>
                <span class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">
                  Page 1 of 1
                </span>
                <Button variant="outline" size="sm" disabled>
                  Next →
                </Button>
              </nav>
            </div>
          </>
        ) : (
          <!-- Empty State -->
          <div class="text-center py-16">
            <div class="mx-auto max-w-md">
              <div class="text-8xl mb-8">📚</div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Content Coming Soon
              </h2>
              <p class="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
                We're crafting amazing content about AI tools for creators. Our team is working on
                comprehensive guides, tutorials, and case studies that will help you leverage AI
                in your creative workflow.
              </p>
              
              <!-- Newsletter Signup Placeholder -->
              <div class="bg-gradient-to-r from-primary-50 to-violet-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Get Notified
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Be the first to know when we publish new content.
                </p>
                <div class="flex flex-col sm:flex-row gap-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    class="flex-1 px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <Button size="sm" class="whitespace-nowrap">
                    Subscribe
                  </Button>
                </div>
              </div>

              <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <Button href="/rss.xml" variant="outline">
                  📡 Subscribe RSS
                </Button>
                <Button href="/about" variant="ghost">
                  Learn More About Us
                </Button>
              </div>
            </div>
          </div>
        )
      }
    </Container>
  </Section>
</PageLayout>