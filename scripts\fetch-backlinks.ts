#!/usr/bin/env tsx

/**
 * Ethical Backlink Discovery Script for CreatorAITools
 * 
 * This script discovers existing backlinks to CreatorAITools content using search APIs.
 * It operates in a read-only manner and does not engage in any link building activities.
 * 
 * Usage: tsx scripts/fetch-backlinks.ts
 * 
 * Environment Variables:
 * - SEARCH_API_KEY: Your search API key (Bing Web Search API recommended)
 * - SEARCH_API_ENDPOINT: Search API endpoint URL
 */

import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

interface Backlink {
  url: string;
  title: string;
  description?: string;
  domain: string;
  discoveredDate: string;
  authority?: number;
  lastChecked: string;
}

interface BacklinksData {
  backlinks: Backlink[];
  lastUpdated: string;
  totalCount: number;
  uniqueDomains: number;
}

class BacklinkDiscovery {
  private apiKey: string;
  private apiEndpoint: string;
  private siteUrl = 'creatoraitools.github.io';
  private siteName = 'CreatorAITools';

  constructor() {
    this.apiKey = process.env.SEARCH_API_KEY || '';
    this.apiEndpoint = process.env.SEARCH_API_ENDPOINT || '';

    if (!this.apiKey || !this.apiEndpoint) {
      console.log('⚠️  Search API credentials not configured. Skipping backlink discovery.');
      console.log('   To enable backlink discovery, set SEARCH_API_KEY and SEARCH_API_ENDPOINT secrets.');
      process.exit(0);
    }
  }

  /**
   * Search for backlinks using various search queries
   */
  async searchBacklinks(): Promise<Backlink[]> {
    const queries = [
      `site:${this.siteUrl}`,
      `"${this.siteName}"`,
      `"${this.siteUrl}"`,
      `link:${this.siteUrl}`,
      `"CreatorAITools" -site:${this.siteUrl}`,
      `"AI tools for creators" "${this.siteName}"`,
    ];

    const allBacklinks: Backlink[] = [];
    const seenUrls = new Set<string>();

    for (const query of queries) {
      try {
        console.log(`🔍 Searching for: ${query}`);
        const results = await this.performSearch(query);
        
        for (const result of results) {
          if (!seenUrls.has(result.url) && this.isValidBacklink(result)) {
            seenUrls.add(result.url);
            allBacklinks.push(result);
          }
        }

        // Rate limiting - wait between requests
        await this.sleep(1000);
      } catch (error) {
        console.error(`❌ Error searching for "${query}":`, error);
      }
    }

    return allBacklinks;
  }

  /**
   * Perform search using Bing Web Search API
   */
  private async performSearch(query: string): Promise<Backlink[]> {
    const url = new URL(this.apiEndpoint);
    url.searchParams.set('q', query);
    url.searchParams.set('count', '50');
    url.searchParams.set('offset', '0');
    url.searchParams.set('mkt', 'en-US');
    url.searchParams.set('safeSearch', 'Moderate');

    const response = await fetch(url.toString(), {
      headers: {
        'Ocp-Apim-Subscription-Key': this.apiKey,
        'User-Agent': 'CreatorAITools-BacklinkDiscovery/1.0',
      },
    });

    if (!response.ok) {
      throw new Error(`Search API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const webPages = data.webPages?.value || [];

    return webPages.map((page: any) => ({
      url: page.url,
      title: page.name || 'Untitled',
      description: page.snippet || '',
      domain: new URL(page.url).hostname,
      discoveredDate: new Date().toISOString(),
      lastChecked: new Date().toISOString(),
    }));
  }

  /**
   * Validate if a result is a legitimate backlink
   */
  private isValidBacklink(result: Backlink): boolean {
    const url = result.url.toLowerCase();
    const domain = result.domain.toLowerCase();

    // Exclude our own site
    if (domain.includes(this.siteUrl.toLowerCase())) {
      return false;
    }

    // Exclude common non-backlink domains
    const excludedDomains = [
      'google.com',
      'bing.com',
      'yahoo.com',
      'duckduckgo.com',
      'facebook.com',
      'twitter.com',
      'linkedin.com',
      'pinterest.com',
      'instagram.com',
      'youtube.com',
      'tiktok.com',
      'reddit.com', // May include but filter carefully
      'quora.com',
      'stackoverflow.com',
      'github.com', // Our own repo
    ];

    if (excludedDomains.some(excluded => domain.includes(excluded))) {
      return false;
    }

    // Exclude URLs that don't seem to be content pages
    const excludedPaths = [
      '/search',
      '/login',
      '/register',
      '/admin',
      '/api/',
      '.pdf',
      '.doc',
      '.zip',
    ];

    if (excludedPaths.some(path => url.includes(path))) {
      return false;
    }

    // Must have a reasonable title
    if (!result.title || result.title.length < 10) {
      return false;
    }

    return true;
  }

  /**
   * Load existing backlinks data
   */
  private loadExistingBacklinks(): BacklinksData {
    const dataPath = join(process.cwd(), 'src/data/backlinks.json');
    
    try {
      const content = readFileSync(dataPath, 'utf-8');
      const data = JSON.parse(content);
      
      // Handle both old format (array) and new format (object)
      if (Array.isArray(data)) {
        return {
          backlinks: data,
          lastUpdated: new Date().toISOString(),
          totalCount: data.length,
          uniqueDomains: new Set(data.map(b => b.domain)).size,
        };
      }
      
      return data;
    } catch (error) {
      console.log('📝 No existing backlinks data found, starting fresh.');
      return {
        backlinks: [],
        lastUpdated: new Date().toISOString(),
        totalCount: 0,
        uniqueDomains: 0,
      };
    }
  }

  /**
   * Merge new backlinks with existing ones
   */
  private mergeBacklinks(existing: Backlink[], newBacklinks: Backlink[]): Backlink[] {
    const existingUrls = new Set(existing.map(b => b.url));
    const merged = [...existing];

    for (const newBacklink of newBacklinks) {
      if (!existingUrls.has(newBacklink.url)) {
        merged.push(newBacklink);
        console.log(`✅ New backlink discovered: ${newBacklink.domain}`);
      }
    }

    // Update lastChecked for all backlinks
    return merged.map(backlink => ({
      ...backlink,
      lastChecked: new Date().toISOString(),
    }));
  }

  /**
   * Save backlinks data to JSON file
   */
  private saveBacklinks(backlinks: Backlink[]): void {
    const dataPath = join(process.cwd(), 'src/data/backlinks.json');
    
    const data: BacklinksData = {
      backlinks: backlinks.sort((a, b) => 
        new Date(b.discoveredDate).getTime() - new Date(a.discoveredDate).getTime()
      ),
      lastUpdated: new Date().toISOString(),
      totalCount: backlinks.length,
      uniqueDomains: new Set(backlinks.map(b => b.domain)).size,
    };

    writeFileSync(dataPath, JSON.stringify(data, null, 2));
    console.log(`💾 Saved ${data.totalCount} backlinks from ${data.uniqueDomains} unique domains`);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Main execution function
   */
  async run(): Promise<void> {
    console.log('🚀 Starting ethical backlink discovery for CreatorAITools...');
    console.log('📋 This process only discovers existing backlinks and does not engage in link building.');

    try {
      // Load existing data
      const existingData = this.loadExistingBacklinks();
      console.log(`📊 Found ${existingData.totalCount} existing backlinks`);

      // Search for new backlinks
      const newBacklinks = await this.searchBacklinks();
      console.log(`🔍 Discovered ${newBacklinks.length} potential new backlinks`);

      // Merge and save
      const mergedBacklinks = this.mergeBacklinks(existingData.backlinks, newBacklinks);
      this.saveBacklinks(mergedBacklinks);

      const newCount = mergedBacklinks.length - existingData.totalCount;
      if (newCount > 0) {
        console.log(`🎉 Added ${newCount} new backlinks!`);
      } else {
        console.log('✨ No new backlinks found this time.');
      }

      console.log('✅ Backlink discovery completed successfully!');
    } catch (error) {
      console.error('❌ Error during backlink discovery:', error);
      process.exit(1);
    }
  }
}

// Run the script
if (require.main === module) {
  const discovery = new BacklinkDiscovery();
  discovery.run().catch(console.error);
}