---
import { SITE } from '../../config/site';
import Container from '../layout/Container.astro';
import AdSlot from '../ads/AdSlot.astro';
---

<footer class="border-t border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900">
  <Container>
    <!-- Main Footer Content -->
    <div class="py-12 lg:py-16">
      <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
        <!-- Brand Section -->
        <div class="sm:col-span-2 lg:col-span-1">
          <div class="flex items-center space-x-2 mb-4">
            <div
              class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-fuchsia-500 text-white font-bold text-sm"
            >
              CA
            </div>
            <span class="text-lg font-bold text-gray-900 dark:text-white">{SITE.name}</span>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {SITE.tagline}
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-500">
            Discover practical AI tools and strategies for content creators.
          </p>
        </div>

        <!-- Navigation Links -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-4">Navigation</h3>
          <ul class="space-y-3">
            {
              SITE.navigation.slice(0, 4).map((item) => (
                <li>
                  <a
                    href={item.href}
                    class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
                  >
                    {item.name}
                  </a>
                </li>
              ))
            }
          </ul>
        </div>

        <!-- Categories -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-4">Categories</h3>
          <ul class="space-y-3">
            {
              SITE.categories.map((category) => (
                <li>
                  <a
                    href={`/tags/${category.toLowerCase().replace(/\s+/g, '-')}`}
                    class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
                  >
                    {category}
                  </a>
                </li>
              ))
            }
          </ul>
        </div>

        <!-- Resources & Legal -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-4">Resources</h3>
          <ul class="space-y-3">
            <li>
              <a
                href="/rss.xml"
                class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
              >
                RSS Feed
              </a>
            </li>
            <li>
              <a
                href="/sitemap.xml"
                class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
              >
                Sitemap
              </a>
            </li>
            <li>
              <a
                href="/privacy"
                class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
              >
                Privacy Policy
              </a>
            </li>
            <li>
              <a
                href="/terms"
                class="text-sm text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
              >
                Terms of Service
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-gray-200 py-6 dark:border-gray-800">
      <div class="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
        <!-- Copyright -->
        <div class="text-sm text-gray-500 dark:text-gray-400">
          © {new Date().getFullYear()}
          {SITE.name}. All rights reserved.
        </div>

        <!-- Social Links -->
        <div class="flex items-center space-x-4">
          <a
            href={SITE.social.github}
            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
            aria-label="GitHub"
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path
                fill-rule="evenodd"
                d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                clip-rule="evenodd"></path>
            </svg>
          </a>

          <a
            href={SITE.social.twitter}
            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
            aria-label="Twitter"
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path
                d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
              ></path>
            </svg>
          </a>

          <a
            href={`mailto:${SITE.social.email}`}
            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
            aria-label="Email"
          >
            <svg
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"
              ></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </Container>
</footer>

<style>
  /* Ensure footer links are touch-friendly on mobile */
  @media (max-width: 768px) {
    footer a {
      min-height: 44px;
      display: flex;
      align-items: center;
    }

    /* Stack social links vertically on very small screens */
    @media (max-width: 480px) {
      .flex.items-center.space-x-4 {
        flex-direction: column;
        space-x: 0;
        gap: 1rem;
      }
    }
  }
</style>
