# CreatorAITools

> **Micro-niche playbooks for AI-powered creators**

A modern, mobile-first static blog built with Astro, featuring AI tool reviews, tutorials, and strategies for content creators. Optimized for performance, SEO, and accessibility.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn

### One-Command Setup

```bash
# Clone and setup
git clone https://github.com/yourusername/creatoraitools.git
cd creatoraitools
npm install
npm run dev
```

Your site will be available at `http://localhost:4321/creatoraitools`

## 📁 Project Structure

```
creatoraitools/
├── public/                 # Static assets
│   ├── favicon.svg
│   ├── manifest.json      # PWA manifest
│   ├── sw.js             # Service worker
│   └── performance-monitor.js
├── src/
│   ├── components/        # Reusable Astro components
│   │   ├── ui/           # UI components (Header, Footer, Button)
│   │   ├── content/      # Content components (PostCard, TOC)
│   │   ├── layout/       # Layout components (Container, Section)
│   │   ├── interactive/  # React components (ThemeToggle, Search)
│   │   └── ads/          # Ad components (disabled by default)
│   ├── layouts/          # Page layouts
│   │   ├── BaseLayout.astro    # Base HTML structure
│   │   ├── PageLayout.astro    # Standard page layout
│   │   └── PostLayout.astro    # Blog post layout
│   ├── pages/            # File-based routing
│   │   ├── index.astro         # Homepage
│   │   ├── blog/              # Blog pages
│   │   ├── tags/              # Tag pages
│   │   ├── about.astro
│   │   ├── contact.astro
│   │   ├── backlinks.astro
│   │   ├── rss.xml.ts         # RSS feed
│   │   └── robots.txt.ts      # Robots.txt
│   ├── content/          # Content Collections
│   │   ├── config.ts          # Zod schemas
│   │   └── posts/             # Blog posts (MDX)
│   ├── config/           # Configuration files
│   │   ├── site.ts            # Site metadata
│   │   ├── seo.ts             # SEO helpers
│   │   └── ads.ts             # Ad configuration
│   ├── lib/              # Utility functions
│   ├── styles/           # CSS files
│   └── data/             # Static data files
├── scripts/              # Build and automation scripts
├── .github/workflows/    # GitHub Actions
└── astro.config.mjs     # Astro configuration
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run preview         # Preview production build

# Quality & Testing
npm run type-check      # TypeScript checking
npm run lint           # ESLint (placeholder)
npm run format         # Format with Prettier
npm run format:check   # Check formatting

# Performance
npm run lighthouse     # Run Lighthouse audit
npm run lighthouse:mobile  # Mobile-focused audit
npm run analyze       # Bundle analysis

# Content
npm run new:post "Title"  # Create new blog post
```

### Local Development

1. **Start the dev server:**
   ```bash
   npm run dev
   ```

2. **Access your site:**
   - Local: `http://localhost:4321/creatoraitools`
   - Network: Use `--host` flag to expose

3. **Hot reloading:** Changes to `.astro`, `.ts`, `.tsx`, and `.css` files trigger automatic reloads.

## 📝 Content Management

### Creating Blog Posts

#### Using the CLI (Recommended)

```bash
npm run new:post "Your Amazing Post Title"
```

This creates a new MDX file in `src/content/posts/` with proper frontmatter.

#### Manual Creation

Create a new `.mdx` file in `src/content/posts/`:

```mdx
---
title: "Your Post Title"
description: "A compelling description for SEO and social sharing"
publishDate: 2024-08-15
category: "AI Writing"  # Must match schema enum
tags: ["AI Tools", "Content Creation", "Productivity"]
draft: false
author: "CreatorAITools Team"
readingTime: 8
---

# Your Content Here

Write your post content in MDX format...
```

#### Content Collections Schema

Posts must follow the Zod schema defined in `src/content/config.ts`:

- **Required:** `title`, `description`, `publishDate`, `category`, `tags`
- **Optional:** `updatedDate`, `coverImage`, `draft`, `author`, `readingTime`
- **Categories:** "AI Writing", "Thumbnails", "Video Automation", "SEO for Creators"

### Draft Handling

- Set `draft: true` in frontmatter to exclude from production
- Draft posts won't appear in:
  - Blog index
  - RSS feeds
  - Sitemaps
  - Tag pages

## 🎨 Customization

### Branding

Update site information in `src/config/site.ts`:

```typescript
export const SITE = {
  name: 'Your Site Name',
  title: 'Your Site Title',
  description: 'Your site description',
  url: 'https://yoursite.com',
  // ... other settings
};
```

### Styling

- **Global styles:** `src/styles/global.css`
- **Component styles:** `src/styles/components.css`
- **Mobile styles:** `src/styles/mobile.css`
- **Tailwind config:** `tailwind.config.cjs`

### Theme Colors

The site uses a gradient color system (indigo → violet → fuchsia). Update CSS custom properties in `src/styles/global.css`:

```css
:root {
  --gradient-from: 99 102 241;   /* indigo-500 */
  --gradient-via: 139 92 246;    /* violet-500 */
  --gradient-to: 217 70 239;     /* fuchsia-500 */
}
```

## 💰 Ads Configuration

Ads are **disabled by default**. To enable PropellerAds:

### 1. Update Configuration

Edit `src/config/ads.ts`:

```typescript
export const ADS_CONFIG = {
  enabled: true,  // Enable ads
  
  propellerAds: {
    publisherId: 'YOUR_ACTUAL_PUBLISHER_ID',
    units: {
      inPagePush: {
        id: 'YOUR_IN_PAGE_PUSH_ID',
        enabled: true,
      },
      // ... configure other ad units
    },
  },
};
```

### 2. Add Your PropellerAds IDs

Replace placeholder IDs with your actual PropellerAds unit IDs.

### 3. Privacy Compliance

The consent banner will automatically appear when ads are enabled. Customize the privacy policy and terms pages as needed.

## 🔗 Backlink Discovery

### Automated Backlink Finder

The site includes an ethical, read-only backlink discovery system:

### 1. Set Up Search API

Add GitHub repository secrets:

- `SEARCH_API_KEY`: Your Bing Web Search API key
- `SEARCH_API_ENDPOINT`: Bing API endpoint URL

### 2. How It Works

- **Weekly automation:** GitHub Action runs every Sunday
- **Search queries:** Finds mentions of your site across the web
- **Data storage:** Updates `src/data/backlinks.json`
- **Display:** Renders on `/backlinks` page

### 3. Manual Execution

```bash
# Set environment variables
export SEARCH_API_KEY="your-key"
export SEARCH_API_ENDPOINT="https://api.bing.microsoft.com/v7.0/search"

# Run the script
npx tsx scripts/fetch-backlinks.ts
```

### 4. Ethical Guidelines

- **Read-only:** Only discovers existing backlinks
- **No outreach:** No link building or spam
- **Respectful:** Includes crawl delays and rate limiting

## 🚀 Deployment

### GitHub Pages (Recommended)

#### 1. Repository Setup

1. Create a GitHub repository
2. Push your code to the `main` branch
3. Go to Settings → Pages
4. Set source to "GitHub Actions"

#### 2. Automatic Deployment

The included GitHub Action (`.github/workflows/deploy.yml`) will:

- Build the site on every push to `main`
- Run quality checks (TypeScript, linting, formatting)
- Deploy to GitHub Pages
- Run Lighthouse audits

#### 3. Custom Domain (Optional)

1. Add `CNAME` file to `public/` directory
2. Update `site` and `base` in `astro.config.mjs`
3. Configure DNS settings

### Other Platforms

The site builds to static HTML and can be deployed anywhere:

- **Netlify:** Connect GitHub repo, build command: `npm run build`
- **Vercel:** Import project, framework preset: Astro
- **Cloudflare Pages:** Connect repo, build command: `npm run build`

## 📊 Performance & SEO

### Built-in Optimizations

- **Mobile-first:** Responsive design with 44px+ touch targets
- **Core Web Vitals:** Optimized for LCP < 2.5s, FID < 100ms, CLS < 0.1
- **SEO:** Structured data, meta tags, sitemaps, RSS feeds
- **PWA:** Service worker, offline support, app manifest
- **Accessibility:** WCAG 2.1 AA compliance

### Performance Monitoring

The site includes automatic performance tracking:

- **Core Web Vitals:** LCP, FID, CLS monitoring
- **Mobile metrics:** Device memory, connection speed, battery
- **Debug tools:** `window.CreatorAIToolsPerf` object for debugging

### Lighthouse Audits

```bash
# Run local audits
npm run lighthouse        # Desktop audit
npm run lighthouse:mobile # Mobile audit (primary)
```

**Target Scores:**
- Mobile Performance: ≥ 90
- Desktop Performance: ≥ 95
- SEO: ≥ 95
- Accessibility: ≥ 95
- Best Practices: ≥ 90

## 🔧 Advanced Configuration

### Astro Configuration

Key settings in `astro.config.mjs`:

```javascript
export default defineConfig({
  site: 'https://yourusername.github.io',
  base: '/creatoraitools',  // For GitHub Pages subdirectory
  output: 'static',         // Static site generation
  
  integrations: [
    tailwindcss(),          // Tailwind CSS
    mdx(),                  // MDX support
    react(),                // React (minimal usage)
    sitemap(),              // Automatic sitemap
  ],
});
```

### Content Collections

Extend the schema in `src/content/config.ts`:

```typescript
const postsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    // Add new fields here
    featured: z.boolean().default(false),
    series: z.string().optional(),
    // ... existing fields
  }),
});
```

### SEO Customization

Modify SEO helpers in `src/config/seo.ts`:

- Update structured data schemas
- Customize meta tag generation
- Add new social media platforms

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch:** `git checkout -b feature/amazing-feature`
3. **Make changes and test:** `npm run dev`
4. **Run quality checks:** `npm run type-check && npm run format:check`
5. **Commit changes:** `git commit -m 'Add amazing feature'`
6. **Push to branch:** `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Code Standards

- **TypeScript:** Strict mode enabled
- **Formatting:** Prettier with Astro plugin
- **Linting:** ESLint with Astro and accessibility rules
- **Commits:** Conventional commit format preferred

## 📚 Resources

### Astro Documentation

- [Astro Docs](https://docs.astro.build/)
- [Content Collections](https://docs.astro.build/en/guides/content-collections/)
- [Astro Integrations](https://astro.build/integrations/)

### Performance & SEO

- [Core Web Vitals](https://web.dev/vitals/)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)
- [Schema.org](https://schema.org/)

### AI Tools & Content Creation

- [OpenAI API](https://platform.openai.com/)
- [Jasper AI](https://www.jasper.ai/)
- [Copy.ai](https://www.copy.ai/)

## 🐛 Troubleshooting

### Common Issues

#### Build Errors

**Empty Content Collections:**
```bash
# Error: Collection "posts" does not exist or is empty
# Solution: Add at least one post or check content/config.ts
```

**TypeScript Errors:**
```bash
# Run type checking
npm run type-check

# Fix common issues
npm run format
```

#### Development Issues

**Port Already in Use:**
```bash
# Astro will automatically try the next available port
# Or specify a port: npm run dev -- --port 3000
```

**Slow Hot Reloading:**
```bash
# Clear Astro cache
rm -rf .astro
npm run dev
```

#### Performance Issues

**Large Bundle Size:**
```bash
# Analyze bundle
npm run analyze

# Check for unused dependencies
npx depcheck
```

### Getting Help

- **GitHub Issues:** Report bugs and feature requests
- **Discussions:** Ask questions and share ideas
- **Documentation:** Check this README and Astro docs

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Astro Team:** For the amazing static site generator
- **Tailwind CSS:** For the utility-first CSS framework
- **Vercel:** For inspiration on modern web development
- **Open Source Community:** For the tools and libraries that make this possible

---

**Ready to create amazing content with AI tools?** Start by adding your first blog post and customizing the site to match your brand. The foundation is built for performance, SEO, and user experience – now make it yours!

For questions or support, please open an issue or start a discussion on GitHub.