---
import CopyButton from '../interactive/CopyButton';

export interface Props {
  code: string;
  language?: string;
  title?: string;

  class?: string;
}

const { 
  code, 
  language = 'text', 
  title, 

  class: className 
} = Astro.props;

// Clean up the code
const cleanCode = code.trim();

---

<div class={`code-block-container ${className || ''}`}>
  {title && (
    <div class="code-block-header">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="code-block-title">{title}</span>
          {language && (
            <span class="code-block-language">{language}</span>
          )}
        </div>
        <CopyButton 
          text={cleanCode} 
          className="code-copy-button"
          client:load
        />
      </div>
    </div>
  )}
  
  <div class="code-block-wrapper">
    <pre class={`code-block language-${language}`}><code class={`language-${language}`}>{cleanCode}</code></pre>
    
    {!title && (
      <div class="code-block-actions">
        <CopyButton 
          text={cleanCode} 
          className="code-copy-button-floating"
          client:load
        >
          <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </CopyButton>
      </div>
    )}
  </div>
</div>

<style>
  .code-block-container {
    @apply relative my-6 rounded-lg overflow-hidden;
    @apply bg-gray-900 dark:bg-gray-950;
    @apply border border-gray-700 dark:border-gray-800;
  }

  .code-block-header {
    @apply px-4 py-3 bg-gray-800 dark:bg-gray-900;
    @apply border-b border-gray-700 dark:border-gray-800;
  }

  .code-block-title {
    @apply text-sm font-medium text-gray-200 dark:text-gray-300;
  }

  .code-block-language {
    @apply px-2 py-1 text-xs font-mono;
    @apply bg-gray-700 dark:bg-gray-800 text-gray-300 dark:text-gray-400;
    @apply rounded border border-gray-600 dark:border-gray-700;
  }

  .code-block-wrapper {
    @apply relative;
  }

  .code-block {
    @apply p-4 text-sm leading-relaxed;
    @apply bg-gray-900 dark:bg-gray-950 text-gray-100;
    @apply overflow-x-auto;
    @apply font-mono;
    
    /* Mobile optimizations */
    -webkit-overflow-scrolling: touch;
    
    /* Ensure proper spacing */
    tab-size: 2;
    -moz-tab-size: 2;
  }

  .code-block code {
    @apply text-gray-100;
    @apply font-mono text-sm;
  }

  .code-block-actions {
    @apply absolute top-2 right-2;
  }

  .code-copy-button {
    @apply text-xs px-2 py-1;
    @apply bg-gray-700 hover:bg-gray-600;
    @apply text-gray-200 border-gray-600;
  }

  .code-copy-button-floating {
    @apply p-2 text-xs;
    @apply bg-gray-800/90 hover:bg-gray-700/90;
    @apply text-gray-200 border-gray-600;
    @apply backdrop-blur-sm;
    @apply opacity-0 group-hover:opacity-100;
    @apply transition-opacity duration-200;
  }

  .code-block-container:hover .code-copy-button-floating {
    @apply opacity-100;
  }

  /* Mobile-specific styles */
  @media (max-width: 768px) {
    .code-block {
      @apply text-xs px-3 py-3;
      
      /* Better horizontal scrolling on mobile */
      scroll-snap-type: x mandatory;
      scroll-padding: 1rem;
    }

    .code-copy-button-floating {
      @apply opacity-100; /* Always visible on mobile */
      @apply top-1 right-1;
    }

    .code-block-header {
      @apply px-3 py-2;
    }

    .code-block-title {
      @apply text-xs;
    }

    .code-block-language {
      @apply text-xs px-1.5 py-0.5;
    }
  }

  /* Syntax highlighting will be handled by Prism.js or Shiki */
  /* These are fallback styles */
  .code-block .token.comment {
    @apply text-gray-500;
  }

  .code-block .token.string {
    @apply text-green-400;
  }

  .code-block .token.number {
    @apply text-blue-400;
  }

  .code-block .token.keyword {
    @apply text-purple-400;
  }

  .code-block .token.function {
    @apply text-yellow-400;
  }

  .code-block .token.operator {
    @apply text-pink-400;
  }

  /* Line numbers (if enabled) */
  .code-block.line-numbers {
    @apply pl-12;
  }

  .code-block.line-numbers::before {
    content: counter(line-number);
    counter-increment: line-number;
    @apply absolute left-0 top-0 bottom-0;
    @apply w-10 px-2 py-4;
    @apply text-gray-500 text-right text-xs;
    @apply border-r border-gray-700;
    @apply bg-gray-800;
  }

  /* Scrollbar styling for webkit browsers */
  .code-block::-webkit-scrollbar {
    height: 8px;
  }

  .code-block::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  .code-block::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded;
  }

  .code-block::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }

  /* Focus styles for accessibility */
  .code-block:focus {
    @apply outline-none ring-2 ring-indigo-500 ring-offset-2 ring-offset-gray-900;
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .code-block-container {
      @apply border-2 border-white;
    }

    .code-block {
      @apply bg-black text-white;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .code-copy-button-floating {
      @apply transition-none;
    }
  }
</style>