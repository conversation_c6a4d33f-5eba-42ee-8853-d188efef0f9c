---
export interface Props {
  variant?: 'hero' | 'subtle' | 'accent';
  class?: string;
  as?: keyof HTMLElementTagNameMap;
}

const { 
  variant = 'subtle',
  class: className = '',
  as: Element = 'section'
} = Astro.props;

const variantClasses = {
  hero: 'bg-gradient-to-br from-indigo-500 via-violet-500 to-fuchsia-500',
  subtle: 'bg-gradient-to-br from-indigo-50 via-violet-50 to-fuchsia-50 dark:from-indigo-950 dark:via-violet-950 dark:to-fuchsia-950',
  accent: 'bg-gradient-to-r from-indigo-600 via-violet-600 to-fuchsia-600'
};
---

<Element class={`relative overflow-hidden ${variantClasses[variant]} ${className}`}>
  <!-- Gradient Mesh Background -->
  <div class="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 dark:from-black/20 dark:to-white/10"></div>
  
  <!-- Glass Effect Overlay -->
  {variant === 'hero' && (
    <div class="absolute inset-0 bg-white/10 backdrop-blur-sm dark:bg-black/10"></div>
  )}
  
  <!-- Content -->
  <div class="relative z-10">
    <slot />
  </div>
</Element>