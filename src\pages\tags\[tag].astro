---
// Dynamic route for individual tag pages
import { getCollection, type CollectionEntry } from 'astro:content';
import PageLayout from '../../layouts/PageLayout.astro';
import Container from '../../components/layout/Container.astro';
import Section from '../../components/layout/Section.astro';
import PostCard from '../../components/content/PostCard.astro';
import Button from '../../components/ui/Button.astro';
import { SITE } from '../../config/site';

// TODO: Replace with Content Collections in Phase 3
export async function getStaticPaths() {
  // Generate paths for main categories
  const categoryPaths = SITE.categories.map((category) => ({
    params: { tag: category.toLowerCase().replace(/\s+/g, '-') },
    props: { 
      tagName: category,
      isCategory: true,
    },
  }));

  // Add additional common tags
  const additionalTags = ['automation', 'productivity', 'tools', 'tutorials', 'case-studies', 'beginner'];
  const tagPaths = additionalTags.map((tag) => ({
    params: { tag },
    props: { 
      tagName: tag.charAt(0).toUpperCase() + tag.slice(1).replace('-', ' '),
      isCategory: false,
    },
  }));

  return [...categoryPaths, ...tagPaths];
}

const { tag } = Astro.params;
const { tagName, isCategory } = Astro.props;

// Get all published posts and filter by tag
let allPosts: Array<CollectionEntry<'posts'>> = [];
try {
  allPosts = await getCollection('posts', ({ data }) => !data.draft);
} catch (error) {
  // Collection doesn't exist or is empty
  allPosts = [];
}

// Filter posts by tag (case-insensitive)
const posts = allPosts.filter((post: CollectionEntry<'posts'>) => {
  const postTags = post.data.tags.map((t: string) => t.toLowerCase().replace(/\s+/g, '-'));
  const postCategory = post.data.category.toLowerCase().replace(/\s+/g, '-');
  return postTags.includes(tag) || postCategory === tag;
}).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());

const seo = {
  title: `${tagName} - AI Tools & Strategies`,
  description: `Discover AI tools and strategies for ${tagName.toLowerCase()}. Learn practical techniques to enhance your creative workflow.`,
  canonical: `/tags/${tag}`,
};
---

<PageLayout {...seo}>
  <!-- Page Header -->
  <Section class="bg-gradient-to-br from-primary-50 to-violet-50 dark:from-gray-900 dark:to-gray-800">
    <Container>
      <div class="mx-auto max-w-3xl text-center py-12">
        <!-- Breadcrumb -->
        <nav class="mb-6" aria-label="Breadcrumb">
          <ol class="flex items-center justify-center space-x-2 text-sm">
            <li><a href="/" class="text-gray-500 hover:text-primary-600">Home</a></li>
            <li class="text-gray-400">/</li>
            <li><a href="/tags" class="text-gray-500 hover:text-primary-600">Tags</a></li>
            <li class="text-gray-400">/</li>
            <li class="text-gray-900 dark:text-white font-medium">{tagName}</li>
          </ol>
        </nav>

        <h1 class="text-fluid-4xl font-bold text-gray-900 dark:text-white mb-6">
          {tagName}
        </h1>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 mb-8">
          {isCategory 
            ? `Explore AI tools and strategies specifically for ${tagName.toLowerCase()}.`
            : `Browse all content tagged with "${tagName}".`
          }
        </p>
        
        <!-- Tag Navigation -->
        <div class="flex flex-wrap justify-center gap-3">
          <Button href="/tags" variant="outline" size="sm">
            All Tags
          </Button>
          {SITE.categories.map((category) => {
            const slug = category.toLowerCase().replace(/\s+/g, '-');
            const isActive = slug === tag;
            return (
              <Button
                href={`/tags/${slug}`}
                variant={isActive ? "primary" : "ghost"}
                size="sm"
                class={isActive ? "" : "text-gray-600 hover:text-primary-600"}
              >
                {category}
              </Button>
            );
          })}
        </div>
      </div>
    </Container>
  </Section>

  <!-- Posts -->
  <Section>
    <Container>
      {posts.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((post) => (
            <PostCard
              title={post.data.title}
              description={post.data.description}
              slug={post.id}
              publishDate={post.data.publishDate}
              tags={post.data.tags}
              category={post.data.category}
              coverImage={post.data.coverImage}
              readingTime={post.data.readingTime}
            />
          ))}
        </div>
      ) : (
        <!-- Empty State -->
        <div class="text-center py-16">
          <div class="text-8xl mb-8">📝</div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Content Coming Soon
          </h2>
          <p class="text-gray-600 dark:text-gray-400 mb-8">
            We're working on amazing content for {tagName.toLowerCase()}. Check back soon for practical guides and tutorials!
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button href="/blog">Browse All Posts</Button>
            <Button href="/rss.xml" variant="outline">Subscribe for Updates</Button>
          </div>
        </div>
      )}
    </Container>
  </Section>
</PageLayout>