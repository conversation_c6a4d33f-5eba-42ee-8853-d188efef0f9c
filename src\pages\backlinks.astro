---
import PageLayout from '../layouts/PageLayout.astro';
import Container from '../components/layout/Container.astro';
import Section from '../components/layout/Section.astro';
import Card from '../components/ui/Card.astro';
import Button from '../components/ui/Button.astro';

const seo = {
  title: 'Backlinks',
  description: 'Discover websites and resources that link to CreatorAITools content.',
  canonical: '/backlinks',
};

// Load backlinks data from JSON file (generated by GitHub Action)
let backlinks: any[] = [];
let lastUpdated = new Date().toLocaleDateString();

try {
  const backlinksData = await import('../data/backlinks.json');
  const data = backlinksData.default;
  backlinks = data.backlinks || [];
  lastUpdated = data.lastUpdated ? new Date(data.lastUpdated).toLocaleDateString() : lastUpdated;
} catch (error) {
  // File doesn't exist yet, show empty state
  backlinks = [];
}

const stats = {
  totalBacklinks: backlinks.length,
  uniqueDomains: backlinks.length > 0 ? new Set(backlinks.map(link => link.domain || new URL(link.url).hostname)).size : 0,
  lastUpdated,
};
---

<PageLayout {...seo}>
  <!-- Page Header -->
  <Section class="bg-gradient-to-br from-primary-50 to-violet-50 dark:from-gray-900 dark:to-gray-800">
    <Container>
      <div class="mx-auto max-w-3xl text-center py-12">
        <h1 class="text-fluid-4xl font-bold text-gray-900 dark:text-white mb-6">
          Backlinks & Mentions
        </h1>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 mb-8">
          Discover websites and resources that reference CreatorAITools content. 
          This page is automatically updated weekly to show where our content is being shared and discussed.
        </p>
        
        <!-- Stats -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
          <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
              {stats.totalBacklinks}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Total Backlinks
            </div>
          </div>
          <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
              {stats.uniqueDomains}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Unique Domains
            </div>
          </div>
          <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
              Weekly
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Auto Updated
            </div>
          </div>
        </div>
      </div>
    </Container>
  </Section>

  <!-- Backlinks Content -->
  <Section>
    <Container>
      {
        backlinks.length > 0 ? (
          <>
            <!-- Filter/Sort Controls -->
            <div class="mb-8 flex flex-wrap items-center justify-between gap-4">
              <div class="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" class="bg-primary-50 text-primary-700 border-primary-200">
                  All ({stats.totalBacklinks})
                </Button>
                <Button variant="ghost" size="sm">
                  High Authority
                </Button>
                <Button variant="ghost" size="sm">
                  Recent
                </Button>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Last updated: {stats.lastUpdated}
              </div>
            </div>

            <!-- Backlinks List -->
            <div class="space-y-4">
              {backlinks.map((backlink, index) => {
                const domain = new URL(backlink.url).hostname;
                return (
                  <Card hover class="group">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors mb-1">
                          <a 
                            href={backlink.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            class="block"
                          >
                            {backlink.title}
                          </a>
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {backlink.description || 'No description available'}
                        </p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>{domain}</span>
                          {backlink.discoveredDate && (
                            <span>Found: {new Date(backlink.discoveredDate).toLocaleDateString()}</span>
                          )}
                          {backlink.authority && (
                            <span class="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded">
                              DA: {backlink.authority}
                            </span>
                          )}
                        </div>
                      </div>
                      <div class="ml-4">
                        <a
                          href={backlink.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm"
                        >
                          Visit →
                        </a>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            <!-- Pagination (if needed) -->
            {backlinks.length > 20 && (
              <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2" aria-label="Pagination">
                  <Button variant="outline" size="sm" disabled>
                    ← Previous
                  </Button>
                  <span class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">
                    Page 1 of 1
                  </span>
                  <Button variant="outline" size="sm" disabled>
                    Next →
                  </Button>
                </nav>
              </div>
            )}
          </>
        ) : (
          <!-- Empty State -->
          <div class="text-center py-16">
            <div class="text-8xl mb-8">🔗</div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Backlinks Coming Soon
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
              Our automated backlink discovery system will start finding and displaying websites that 
              link to CreatorAITools content. This page updates weekly with new discoveries.
            </p>
            
            <!-- How it works -->
            <div class="bg-gradient-to-r from-primary-50 to-violet-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 max-w-2xl mx-auto mb-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                How Our Backlink Discovery Works
              </h3>
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                <div>
                  <div class="text-2xl mb-2">🔍</div>
                  <div class="font-medium text-gray-900 dark:text-white mb-1">Search</div>
                  <div class="text-gray-600 dark:text-gray-400">
                    Weekly automated searches for mentions
                  </div>
                </div>
                <div>
                  <div class="text-2xl mb-2">📊</div>
                  <div class="font-medium text-gray-900 dark:text-white mb-1">Analyze</div>
                  <div class="text-gray-600 dark:text-gray-400">
                    Quality and relevance filtering
                  </div>
                </div>
                <div>
                  <div class="text-2xl mb-2">📋</div>
                  <div class="font-medium text-gray-900 dark:text-white mb-1">Display</div>
                  <div class="text-gray-600 dark:text-gray-400">
                    Organized, searchable results
                  </div>
                </div>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <Button href="/blog">
                Browse Our Content
              </Button>
              <Button href="/about" variant="outline">
                Learn More About Us
              </Button>
            </div>
          </div>
        )
      }
    </Container>
  </Section>

  <!-- About Backlinks Section -->
  <Section class="bg-gray-50 dark:bg-gray-800/50">
    <Container>
      <div class="mx-auto max-w-3xl text-center">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          About This Page
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
          This backlinks page is automatically maintained through ethical, read-only discovery methods. 
          We use search APIs to find mentions of CreatorAITools across the web, helping us understand 
          how our content is being shared and discussed in the creator community.
        </p>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <p>
            <strong>Note:</strong> We only discover and display existing backlinks. We do not engage in 
            link building, outreach, or any form of spam. All backlinks shown here were created 
            organically by other websites.
          </p>
        </div>
      </div>
    </Container>
  </Section>
</PageLayout>