---
export interface Props {
  title: string;
  url: string;
  description?: string;
  class?: string;
}

const { title, url, description, class: className } = Astro.props;

// Generate full URL for sharing
const fullUrl = new URL(url, Astro.site || 'https://creatoraitools.github.io/creatoraitools').toString();
const encodedTitle = encodeURIComponent(title);
const encodedUrl = encodeURIComponent(fullUrl);


// Social sharing URLs
const shareUrls = {
  twitter: `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
  linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
  facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
  reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`,
  hackernews: `https://news.ycombinator.com/submitlink?u=${encodedUrl}&t=${encodedTitle}`,
  email: `mailto:?subject=${encodedTitle}&body=I thought you might find this interesting: ${encodedUrl}`,
};
---

<div class={`share-buttons ${className || ''}`}>
  <!-- Native Share API (Mobile) -->
  <button
    class="share-button native-share lg:hidden"
    onclick="handleNativeShare()"
    title="Share this post"
    aria-label="Share this post"
  >
    <span class="share-icon">📤</span>
    <span class="share-text">Share</span>
  </button>

  <!-- Desktop Share Buttons -->
  <div class="hidden lg:flex items-center space-x-2">
    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Share:</span>
    
    <a
      href={shareUrls.twitter}
      target="_blank"
      rel="noopener noreferrer"
      class="share-button"
      title="Share on Twitter"
      aria-label="Share on Twitter"
    >
      <span class="share-icon">🐦</span>
    </a>

    <a
      href={shareUrls.linkedin}
      target="_blank"
      rel="noopener noreferrer"
      class="share-button"
      title="Share on LinkedIn"
      aria-label="Share on LinkedIn"
    >
      <span class="share-icon">💼</span>
    </a>

    <a
      href={shareUrls.facebook}
      target="_blank"
      rel="noopener noreferrer"
      class="share-button"
      title="Share on Facebook"
      aria-label="Share on Facebook"
    >
      <span class="share-icon">📘</span>
    </a>

    <a
      href={shareUrls.reddit}
      target="_blank"
      rel="noopener noreferrer"
      class="share-button"
      title="Share on Reddit"
      aria-label="Share on Reddit"
    >
      <span class="share-icon">🔴</span>
    </a>

    <button
      class="share-button"
      onclick="copyToClipboard()"
      title="Copy link"
      aria-label="Copy link to clipboard"
    >
      <span class="share-icon">🔗</span>
    </button>

    <a
      href={shareUrls.email}
      class="share-button"
      title="Share via email"
      aria-label="Share via email"
    >
      <span class="share-icon">📧</span>
    </a>
  </div>
</div>

<script is:inline define:vars={{ title, fullUrl, description: description || title }}>
  // Native share API for mobile devices
  function handleNativeShare() {
    if (navigator.share) {
      navigator.share({
        title: title,
        text: description,
        url: fullUrl
      }).catch(err => {
        console.log('Error sharing:', err);
        // Fallback to copy to clipboard
        copyToClipboard();
      });
    } else {
      // Fallback to copy to clipboard
      copyToClipboard();
    }
  }

  // Copy link to clipboard
  function copyToClipboard() {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(fullUrl).then(() => {
        showCopyFeedback();
      }).catch(err => {
        console.error('Failed to copy:', err);
        // Fallback for older browsers
        fallbackCopyToClipboard(fullUrl);
      });
    } else {
      fallbackCopyToClipboard(fullUrl);
    }
  }

  // Fallback copy method for older browsers
  function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      showCopyFeedback();
    } catch (err) {
      console.error('Fallback copy failed:', err);
      alert('Unable to copy link. Please copy manually: ' + text);
    }
    
    document.body.removeChild(textArea);
  }

  // Show copy feedback
  function showCopyFeedback() {
    // Create temporary feedback element
    const feedback = document.createElement('div');
    feedback.textContent = 'Link copied!';
    feedback.className = 'copy-feedback';
    feedback.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      z-index: 1000;
      animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(feedback);
    
    // Remove after 3 seconds
    setTimeout(() => {
      feedback.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback);
        }
      }, 300);
    }, 3000);
  }

  // Make functions globally available
  window.handleNativeShare = handleNativeShare;
  window.copyToClipboard = copyToClipboard;
</script>

<style>
  .share-buttons {
    @apply flex items-center;
  }

  .share-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    min-width: 32px;
    min-height: 32px;
    border-radius: 9999px;
    background-color: rgb(243 244 246);
    color: rgb(75 85 99);
    transition: all 0.2s ease;
  }

  .share-button:hover {
    background-color: rgb(229 231 235);
    color: rgb(17 24 39);
  }

  [data-theme="dark"] .share-button {
    background-color: rgb(31 41 55);
    color: rgb(156 163 175);
  }

  [data-theme="dark"] .share-button:hover {
    background-color: rgb(55 65 81);
    color: rgb(255 255 255);
  }

  .share-button.native-share {
    padding: 0.5rem 0.75rem;
    width: auto;
    height: auto;
    gap: 0.5rem;
  }

  .share-icon {
    font-size: 0.875rem;
  }

  .share-text {
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Animations */
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  /* Focus styles for accessibility */
  .share-button:focus-visible {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
  }

  /* Hover effects */
  .share-button:hover .share-icon {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .share-button {
      @apply w-10 h-10;
      min-width: 40px;
      min-height: 40px;
    }
  }
</style>