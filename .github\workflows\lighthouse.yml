name: Lighthouse CI

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build site
        run: npm run build

      - name: Serve site
        run: |
          npm install -g serve
          serve -s dist -l 3000 &
          sleep 5

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Run Lighthouse CI (Mobile-first)
        run: |
          lhci autorun \
            --collect.url=http://localhost:3000 \
            --collect.settings.chromeFlags="--no-sandbox --headless" \
            --collect.settings.preset="perf" \
            --collect.settings.formFactor="mobile" \
            --assert.assertions.categories:performance=0.9 \
            --assert.assertions.categories:accessibility=0.95 \
            --assert.assertions.categories:best-practices=0.9 \
            --assert.assertions.categories:seo=0.95 \
            --upload.target=temporary-public-storage
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Run Lighthouse CI (Desktop)
        run: |
          lhci autorun \
            --collect.url=http://localhost:3000 \
            --collect.settings.chromeFlags="--no-sandbox --headless" \
            --collect.settings.preset="perf" \
            --collect.settings.formFactor="desktop" \
            --assert.assertions.categories:performance=0.95 \
            --assert.assertions.categories:accessibility=0.95 \
            --assert.assertions.categories:best-practices=0.9 \
            --assert.assertions.categories:seo=0.95 \
            --upload.target=temporary-public-storage
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.GITHUB_TOKEN }}