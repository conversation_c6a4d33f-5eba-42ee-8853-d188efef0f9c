---
import PageLayout from '../layouts/PageLayout.astro';
import GradientSection from '../components/layout/GradientSection.astro';
import Container from '../components/layout/Container.astro';
import Section from '../components/layout/Section.astro';
import Button from '../components/ui/Button.astro';
import Card from '../components/ui/Card.astro';
import { SITE } from '../config/site';

const seo = {
  title: 'Home',
  description: SITE.description,
  canonical: '/',
};
---

<PageLayout {...seo}>
  <!-- Hero Section -->
  <GradientSection variant="hero" class="relative">
    <Section padding="xl">
      <Container>
        <div class="mx-auto max-w-4xl text-center">
          <!-- Hero Content -->
          <h1 class="text-fluid-4xl sm:text-fluid-5xl font-bold text-white mb-6 leading-tight">
            {SITE.name}
          </h1>
          
          <p class="text-fluid-xl text-white/90 mb-8 leading-relaxed max-w-2xl mx-auto">
            {SITE.tagline}
          </p>
          
          <p class="text-fluid-lg text-white/80 mb-10 max-w-3xl mx-auto">
            Discover practical AI tools and strategies for content creators. Learn how to leverage AI for writing, thumbnails, video automation, and SEO optimization.
          </p>
          
          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              href="/blog" 
              size="lg"
              class="bg-white text-gray-900 hover:bg-gray-100 focus:ring-white shadow-xl hover:shadow-2xl min-w-[160px]"
            >
              Start Reading
            </Button>
            
            <Button 
              href="/rss.xml" 
              variant="outline"
              size="lg"
              class="border-white/30 text-white hover:bg-white/10 focus:ring-white backdrop-blur-sm min-w-[160px]"
            >
              Subscribe RSS
            </Button>
          </div>
        </div>
      </Container>
    </Section>
  </GradientSection>

  <!-- Categories Section -->
  <Section>
    <Container>
      <div class="text-center mb-12">
        <h2 class="text-fluid-3xl font-bold text-gray-900 dark:text-white mb-4">
          Explore Categories
        </h2>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Dive into specialized AI tools and techniques for different aspects of content creation.
        </p>
      </div>
      
      <!-- Category Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {SITE.categories.map((category) => {
          const slug = category.toLowerCase().replace(/\s+/g, '-');
          const icons = {
            'ai-writing': '✍️',
            'thumbnails': '🎨', 
            'video-automation': '🎬',
            'seo-for-creators': '📈'
          };
          
          return (
            <Card hover class="text-center group">
              <div class="text-4xl mb-4">{icons[slug] || '🚀'}</div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                {category}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Discover AI tools and strategies for {category.toLowerCase()}.
              </p>
              <Button 
                href={`/tags/${slug}`}
                variant="ghost"
                size="sm"
                class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
              >
                Explore →
              </Button>
            </Card>
          );
        })}
      </div>
    </Container>
  </Section>

  <!-- Latest Posts Section -->
  <Section class="bg-gray-50 dark:bg-gray-800/50">
    <Container>
      <div class="text-center mb-12">
        <h2 class="text-fluid-3xl font-bold text-gray-900 dark:text-white mb-4">
          Latest Posts
        </h2>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Stay updated with the latest AI tools, techniques, and strategies for creators.
        </p>
      </div>
      
      <!-- Empty State (no posts yet) -->
      <div class="text-center py-12">
        <div class="mx-auto max-w-md">
          <div class="text-6xl mb-6">📝</div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Coming Soon
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            We're working on amazing content about AI tools for creators. Check back soon for the latest posts!
          </p>
          <Button 
            href="/rss.xml"
            variant="outline"
          >
            Subscribe for Updates
          </Button>
        </div>
      </div>
    </Container>
  </Section>
</PageLayout>
