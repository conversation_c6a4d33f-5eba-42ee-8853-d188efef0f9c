---
import { ADS_CONFIG } from '../../config/ads';

export interface Props {
  type: 'banner' | 'sidebar' | 'inArticle' | 'footer' | 'inPagePush' | 'interstitial' | 'native';
  class?: string;
  mobileHidden?: boolean;
  desktopHidden?: boolean;
}

const { type, class: className, mobileHidden = false, desktopHidden = false } = Astro.props;

// Check if ads are enabled and this specific ad type is enabled
const adsEnabled = ADS_CONFIG.enabled;
const adUnit = ADS_CONFIG.propellerAds.units[type];
const adEnabled = adUnit?.enabled;

// Check mobile compatibility
const isMobileCompatible = adUnit?.mobileEnabled !== false;

// Generate unique ID for this ad slot
const adId = `ad-${type}-${Math.random().toString(36).substring(2, 11)}`;

// Don't render if ads are disabled or this ad type is disabled
const shouldShowPlaceholder = !adsEnabled || !adEnabled;
const showDevPlaceholder = shouldShowPlaceholder && import.meta.env.DEV;
---

{showDevPlaceholder && (
  <div class={`ad-placeholder ${className || ''}`} data-ad-type={type}>
    <div class="ad-placeholder-content">
      <span class="ad-placeholder-label">Ad Placeholder ({type})</span>
      <span class="ad-placeholder-note">Ads disabled in config</span>
    </div>
  </div>
)}

{!shouldShowPlaceholder && (
<div 
  class={`ad-slot ad-slot-${type} ${className || ''} ${mobileHidden ? 'hidden md:block' : ''} ${desktopHidden ? 'block md:hidden' : ''}`}
  data-ad-type={type}
  data-ad-id={adId}
  data-mobile-compatible={isMobileCompatible}
>
  <!-- Ad Container -->
  <div 
    id={adId}
    class="ad-container"
    data-ad-unit-id={adUnit.id}
    data-publisher-id={ADS_CONFIG.propellerAds.publisherId}
  >
    <!-- Loading placeholder -->
    <div class="ad-loading" id={`${adId}-loading`}>
      <div class="ad-loading-content">
        <div class="ad-loading-spinner"></div>
        <span class="ad-loading-text">Loading...</span>
      </div>
    </div>
    
    <!-- Ad will be inserted here by PropellerAds script -->
  </div>

  <!-- Ad Label (required by many ad networks) -->
  <div class="ad-label">
    <span>Advertisement</span>
  </div>
</div>
)}

<!-- PropellerAds Script (only load once per page) -->
{adsEnabled && (
  <script is:inline define:vars={{ adId, adUnitId: adUnit.id, publisherId: ADS_CONFIG.propellerAds.publisherId, lazyLoad: ADS_CONFIG.performance.lazyLoad }}>
    // Ensure we only load the PropellerAds script once
    if (!window.propellerAdsLoaded) {
      window.propellerAdsLoaded = true;
      
      // Function to load PropellerAds
      function loadPropellerAds() {
        // Check for consent if required
        if (window.adsConsentRequired && !window.adsConsentGiven) {
          console.log('Ad loading blocked - consent required');
          return;
        }

        // Check Do Not Track
        if (navigator.doNotTrack === '1' && window.respectDoNotTrack) {
          console.log('Ad loading blocked - Do Not Track enabled');
          return;
        }

        // Load PropellerAds script
        const script = document.createElement('script');
        script.src = `//d16fk4ms6rqz1v.cloudfront.net/ads/ads.js`;
        script.async = true;
        script.onload = function() {
          // Initialize ads after script loads
          if (window.propellerAds) {
            window.propellerAds.init();
          }
        };
        document.head.appendChild(script);
      }

      // Delay loading if configured
      if (delayLoad > 0) {
        setTimeout(loadPropellerAds, delayLoad);
      } else {
        loadPropellerAds();
      }
    }

    // Hide loading indicator when ad loads
    const adContainer = document.getElementById(adId);
    const loadingIndicator = document.getElementById(adId + '-loading');
    
    if (adContainer && loadingIndicator) {
      // Set up observer to hide loading when ad content appears
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if ad content was added
            const hasAdContent = Array.from(mutation.addedNodes).some(node => 
              node.nodeType === Node.ELEMENT_NODE && 
              (node.classList?.contains('propeller-ad') || node.tagName === 'INS')
            );
            
            if (hasAdContent) {
              loadingIndicator.style.display = 'none';
              observer.disconnect();
            }
          }
        });
      });
      
      observer.observe(adContainer, { childList: true, subtree: true });
      
      // Fallback: hide loading after 10 seconds
      setTimeout(() => {
        if (loadingIndicator) {
          loadingIndicator.style.display = 'none';
        }
        observer.disconnect();
      }, 10000);
    }
  </script>
)}

<style>
  .ad-slot {
    position: relative;
    margin: 1rem 0;
    text-align: center;
  }

  .ad-container {
    min-height: 100px;
    background-color: rgb(249 250 251);
    border: 1px solid rgb(229 231 235);
    border-radius: 0.5rem;
    overflow: hidden;
  }

  [data-theme="dark"] .ad-container {
    background-color: rgb(31 41 55);
    border-color: rgb(55 65 81);
  }

  .ad-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    color: rgb(107 114 128);
  }

  .ad-loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .ad-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgb(229 231 235);
    border-top: 2px solid rgb(107 114 128);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .ad-loading-text {
    font-size: 0.875rem;
    color: rgb(107 114 128);
  }

  .ad-label {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: rgb(156 163 175);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Ad type specific styles */
  .ad-slot-banner {
    margin: 2rem 0;
  }

  .ad-slot-banner .ad-container {
    min-height: 90px;
  }

  .ad-slot-sidebar {
    margin: 1rem 0;
    position: sticky;
    top: 2rem;
  }

  .ad-slot-sidebar .ad-container {
    min-height: 250px;
  }

  .ad-slot-inArticle {
    margin: 3rem 0;
  }

  .ad-slot-inArticle .ad-container {
    min-height: 120px;
  }

  .ad-slot-footer {
    margin: 2rem 0 1rem 0;
  }

  .ad-slot-native .ad-container {
    background: transparent;
    border: none;
    min-height: auto;
  }

  /* Development placeholder styles */
  .ad-placeholder {
    margin: 1rem 0;
    padding: 2rem;
    background: linear-gradient(45deg, #f3f4f6 25%, transparent 25%), 
                linear-gradient(-45deg, #f3f4f6 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f3f4f6 75%), 
                linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    text-align: center;
  }

  .ad-placeholder-content {
    background: white;
    padding: 1rem;
    border-radius: 0.25rem;
    display: inline-block;
  }

  .ad-placeholder-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .ad-placeholder-note {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* Animations */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .ad-slot {
      margin: 1rem -1rem; /* Extend to screen edges on mobile */
    }
    
    .ad-slot-sidebar {
      position: static; /* Don't stick on mobile */
    }
  }

  /* Respect reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .ad-loading-spinner {
      animation: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .ad-container {
      border-width: 2px;
    }
  }
</style>