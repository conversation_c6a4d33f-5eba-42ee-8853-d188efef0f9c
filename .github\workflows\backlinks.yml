name: Update Backlinks

on:
  schedule:
    # Run every Sunday at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch: # Allow manual trigger

permissions:
  contents: write
  pull-requests: write

jobs:
  update-backlinks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install tsx for TypeScript execution
        run: npm install -g tsx

      - name: Fetch backlinks
        run: tsx scripts/fetch-backlinks.ts
        env:
          SEARCH_API_KEY: ${{ secrets.SEARCH_API_KEY }}
          SEARCH_API_ENDPOINT: ${{ secrets.SEARCH_API_ENDPOINT }}

      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Commit and push changes
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add src/data/backlinks.json
          git commit -m "🔗 Update backlinks data - $(date +'%Y-%m-%d')" || exit 0
          git push

      - name: Create summary
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          echo "## 🔗 Backlinks Updated" >> $GITHUB_STEP_SUMMARY
          echo "Backlinks data has been updated with new discoveries." >> $GITHUB_STEP_SUMMARY
          echo "Updated: $(date)" >> $GITHUB_STEP_SUMMARY
      
      - name: No changes summary
        if: steps.verify-changed-files.outputs.changed == 'false'
        run: |
          echo "## 🔗 No New Backlinks" >> $GITHUB_STEP_SUMMARY
          echo "No new backlinks discovered in this run." >> $GITHUB_STEP_SUMMARY
          echo "Checked: $(date)" >> $GITHUB_STEP_SUMMARY