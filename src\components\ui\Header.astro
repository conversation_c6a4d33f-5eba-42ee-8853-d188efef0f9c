---
import { SITE } from '../../config/site';
import Container from '../layout/Container.astro';
import ThemeToggle from '../interactive/ThemeToggle';
import SearchBox from '../interactive/SearchBox';
---

<header class="sticky top-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
  <Container>
    <div class="flex items-center justify-between h-16 lg:h-20">
      <!-- Logo -->
      <div class="flex items-center space-x-3">
        <a
          href="/"
          class="flex items-center space-x-2 group focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg p-1"
          aria-label="CreatorAITools Home"
        >
          <div class="flex h-8 w-8 lg:h-10 lg:w-10 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-fuchsia-500 text-white font-bold text-sm lg:text-base group-hover:scale-105 transition-transform duration-200">
            CA
          </div>
          <span class="text-lg lg:text-xl font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200">
            {SITE.name}
          </span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-8" aria-label="Main navigation">
        {SITE.navigation.map((item) => (
          <a
            href={item.href}
            class="text-gray-700 dark:text-gray-200 hover:text-indigo-600 dark:hover:text-indigo-400 font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded px-2 py-1"
          >
            {item.name}
          </a>
        ))}
      </nav>

      <!-- Desktop Search & Theme Toggle -->
      <div class="hidden lg:flex items-center space-x-4">
        <SearchBox className="w-64" client:load />
        <ThemeToggle client:load />
      </div>

      <!-- Mobile Menu Button -->
      <div class="flex items-center space-x-2 lg:hidden">
        <ThemeToggle client:load />
        <button
          id="mobile-menu-button"
          type="button"
          class="inline-flex items-center justify-center p-2 rounded-lg text-gray-700 dark:text-gray-200 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 active:scale-95"
          aria-controls="mobile-menu"
          aria-expanded="false"
          aria-label="Toggle mobile menu"
        >
          <span class="sr-only">Open main menu</span>
          <!-- Hamburger icon -->
          <svg
            id="menu-icon"
            class="block h-6 w-6 transition-transform duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
          <!-- Close icon -->
          <svg
            id="close-icon"
            class="hidden h-6 w-6 transition-transform duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div
      id="mobile-menu"
      class="lg:hidden fixed inset-0 top-16 z-50 bg-white dark:bg-gray-900 transform translate-x-full transition-transform duration-300 ease-in-out"
      aria-hidden="true"
    >
      <!-- Overlay -->
      <div
        id="mobile-menu-overlay"
        class="absolute inset-0 bg-black/20 dark:bg-black/40 opacity-0 transition-opacity duration-300"
        aria-hidden="true"
      ></div>

      <!-- Menu Content -->
      <div class="relative h-full bg-white dark:bg-gray-900 shadow-xl max-w-sm ml-auto">
        <div class="flex flex-col h-full">
          <!-- Search -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-800">
            <SearchBox placeholder="Search..." client:load />
          </div>

          <!-- Navigation Links -->
          <nav class="flex-1 px-4 py-6 space-y-2" aria-label="Mobile navigation">
            {SITE.navigation.map((item) => (
              <a
                href={item.href}
                class="block px-4 py-3 text-base font-medium text-gray-700 dark:text-gray-200 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 touch-feedback"
              >
                {item.name}
              </a>
            ))}
          </nav>

          <!-- Categories -->
          <div class="px-4 py-6 border-t border-gray-200 dark:border-gray-800">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Categories</h3>
            <div class="space-y-2">
              {SITE.categories.slice(0, 4).map((category) => (
                <a
                  href={`/tags/${category.toLowerCase().replace(/\s+/g, '-')}`}
                  class="block px-4 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 touch-feedback"
                >
                  {category}
                </a>
              ))}
            </div>
          </div>

          <!-- Footer Links -->
          <div class="px-4 py-4 border-t border-gray-200 dark:border-gray-800">
            <div class="flex items-center justify-between">
              <div class="flex space-x-4">
                <a
                  href={SITE.social.github}
                  class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  aria-label="GitHub"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd" />
                  </svg>
                </a>
                <a
                  href={SITE.social.twitter}
                  class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  aria-label="Twitter"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                © {new Date().getFullYear()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Container>
</header>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');
    
    let isMenuOpen = false;

    function toggleMenu() {
      isMenuOpen = !isMenuOpen;
      
      if (isMenuOpen) {
        // Open menu
        mobileMenu?.classList.remove('translate-x-full');
        mobileMenu?.classList.add('translate-x-0');
        mobileMenuOverlay?.classList.remove('opacity-0');
        mobileMenuOverlay?.classList.add('opacity-100');
        menuIcon?.classList.add('hidden');
        closeIcon?.classList.remove('hidden');
        mobileMenuButton?.setAttribute('aria-expanded', 'true');
        mobileMenu?.setAttribute('aria-hidden', 'false');
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(25);
        }
      } else {
        // Close menu
        mobileMenu?.classList.add('translate-x-full');
        mobileMenu?.classList.remove('translate-x-0');
        mobileMenuOverlay?.classList.add('opacity-0');
        mobileMenuOverlay?.classList.remove('opacity-100');
        menuIcon?.classList.remove('hidden');
        closeIcon?.classList.add('hidden');
        mobileMenuButton?.setAttribute('aria-expanded', 'false');
        mobileMenu?.setAttribute('aria-hidden', 'true');
        
        // Restore body scroll
        document.body.style.overflow = '';
      }
    }

    // Toggle menu on button click
    mobileMenuButton?.addEventListener('click', toggleMenu);

    // Close menu on overlay click
    mobileMenuOverlay?.addEventListener('click', toggleMenu);

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && isMenuOpen) {
        toggleMenu();
      }
    });

    // Close menu on navigation
    const mobileNavLinks = mobileMenu?.querySelectorAll('a');
    mobileNavLinks?.forEach(link => {
      link.addEventListener('click', () => {
        if (isMenuOpen) {
          toggleMenu();
        }
      });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 1024 && isMenuOpen) {
        toggleMenu();
      }
    });
  });
</script>

<style>
  /* Ensure header is always on top */
  header {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Mobile menu animations */
  #mobile-menu {
    will-change: transform;
  }

  /* Smooth transitions for icons */
  #menu-icon,
  #close-icon {
    transition: transform 0.2s ease-in-out;
  }

  /* Focus styles for mobile menu items */
  @media (max-width: 1024px) {
    nav a:focus {
      outline: 2px solid #6366f1;
      outline-offset: 2px;
    }
  }

  /* Prevent scroll when menu is open */
  body.menu-open {
    overflow: hidden;
  }
</style>