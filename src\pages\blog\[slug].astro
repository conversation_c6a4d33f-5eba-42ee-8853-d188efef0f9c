---
import { getCollection, type CollectionEntry } from 'astro:content';
import PostLayout from '../../layouts/PostLayout.astro';

export async function getStaticPaths() {
  let posts: CollectionEntry<'posts'>[] = [];

  try {
    posts = await getCollection('posts', ({ data }) => !data.draft);
  } catch (error) {
    // Collection doesn't exist or is empty
    posts = [];
  }

  return posts.map((post) => ({
    params: { slug: post.slug },
    props: { post },
  }));
}

const { post } = Astro.props;
const { Content, headings } = await post.render();
---

<PostLayout
  title={post.data.title}
  description={post.data.description}
  publishDate={post.data.publishDate}
  updatedDate={post.data.updatedDate}
  author={post.data.author}
  tags={post.data.tags}
  category={post.data.category}
  readingTime={post.data.readingTime}
  wordCount={post.data.wordCount}
  headings={headings}
  coverImage={post.data.coverImage}
  coverImageAlt={post.data.coverImageAlt}
>
  <Content />
</PostLayout>