---
import BaseLayout from './BaseLayout.astro';
import Header from '../components/ui/Header.astro';
import Footer from '../components/ui/Footer.astro';
import FloatingActionButton from '../components/ui/FloatingActionButton.astro';

export interface Props {
  title?: string;
  description?: string;
  canonical?: string;
  image?: string;
  imageAlt?: string;
  noindex?: boolean;
  nofollow?: boolean;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
  class?: string;
}

const props = Astro.props;
---

<BaseLayout {...props}>
  <Header />
  
  <main id="main-content" class="flex-1">
    <slot />
  </main>
  
  <Footer />
  
  <!-- Floating Action Buttons -->
  <FloatingActionButton type="scroll-top" position="bottom-right" />
</BaseLayout>