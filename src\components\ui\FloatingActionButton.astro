---
export interface Props {
  type?: 'scroll-top' | 'share' | 'menu' | 'search';
  class?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
}

const { 
  type = 'scroll-top', 
  class: className = '',
  position = 'bottom-right'
} = Astro.props;

const positionClasses = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6',
  'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2'
};
---

<button
  id={`fab-${type}`}
  class={`
    fab fixed z-50 w-14 h-14 
    bg-gradient-to-r from-indigo-500 to-violet-500 
    hover:from-indigo-600 hover:to-violet-600
    text-white rounded-full shadow-lg hover:shadow-xl
    flex items-center justify-center
    transform transition-all duration-300 ease-out
    hover:scale-110 active:scale-95
    focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
    opacity-0 translate-y-16 pointer-events-none
    ${positionClasses[position]}
    ${className}
  `}
  aria-label={
    type === 'scroll-top' ? 'Scroll to top' :
    type === 'share' ? 'Share page' :
    type === 'menu' ? 'Open menu' :
    type === 'search' ? 'Search' : 'Action button'
  }
  title={
    type === 'scroll-top' ? 'Back to top' :
    type === 'share' ? 'Share this page' :
    type === 'menu' ? 'Menu' :
    type === 'search' ? 'Search' : 'Action'
  }
>
  {type === 'scroll-top' && (
    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
    </svg>
  )}
  
  {type === 'share' && (
    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
    </svg>
  )}
  
  {type === 'menu' && (
    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
    </svg>
  )}
  
  {type === 'search' && (
    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  )}
</button>

<script define:vars={{ type }}>
  document.addEventListener('DOMContentLoaded', function() {
    const fab = document.getElementById(`fab-${type}`);
    if (!fab) return;

    let isVisible = false;
    let scrollTimeout;

    function showFab() {
      if (!isVisible) {
        isVisible = true;
        fab.classList.remove('opacity-0', 'translate-y-16', 'pointer-events-none');
        fab.classList.add('opacity-100', 'translate-y-0', 'pointer-events-auto');
      }
    }

    function hideFab() {
      if (isVisible) {
        isVisible = false;
        fab.classList.add('opacity-0', 'translate-y-16', 'pointer-events-none');
        fab.classList.remove('opacity-100', 'translate-y-0', 'pointer-events-auto');
      }
    }

    // Show/hide based on scroll position
    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      if (type === 'scroll-top') {
        if (scrollTop > 300) {
          showFab();
        } else {
          hideFab();
        }
      } else {
        // For other types, show after initial scroll
        if (scrollTop > 100) {
          showFab();
        }
      }

      // Hide during scroll for better performance
      clearTimeout(scrollTimeout);
      if (isVisible && type === 'scroll-top') {
        fab.style.opacity = '0.7';
      }
      
      scrollTimeout = setTimeout(() => {
        if (isVisible && type === 'scroll-top') {
          fab.style.opacity = '1';
        }
      }, 150);
    }

    // Handle click actions
    function handleClick() {
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }

      switch (type) {
        case 'scroll-top':
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
          break;
          
        case 'share':
          handleShare();
          break;
          
        case 'menu':
          // Trigger mobile menu
          const mobileMenuButton = document.getElementById('mobile-menu-button');
          if (mobileMenuButton) {
            mobileMenuButton.click();
          }
          break;
          
        case 'search':
          // Focus search input
          const searchInput = document.querySelector('input[type="search"]');
          if (searchInput) {
            searchInput.focus();
          }
          break;
      }
    }

    // Share functionality
    async function handleShare() {
      const shareData = {
        title: document.title,
        text: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
        url: window.location.href
      };

      try {
        if (navigator.share) {
          await navigator.share(shareData);
        } else {
          // Fallback: copy to clipboard
          await navigator.clipboard.writeText(window.location.href);
          showToast('Link copied to clipboard!');
        }
      } catch (error) {
        console.error('Error sharing:', error);
        // Fallback: copy to clipboard
        try {
          await navigator.clipboard.writeText(window.location.href);
          showToast('Link copied to clipboard!');
        } catch (clipboardError) {
          console.error('Clipboard error:', clipboardError);
        }
      }
    }

    // Show toast notification
    function showToast(message) {
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-gray-900 text-white px-4 py-2 rounded-lg shadow-lg z-50 text-sm text-center animate-slide-in-right';
      toast.textContent = message;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      }, 3000);
    }

    // Event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    fab.addEventListener('click', handleClick);

    // Initial check
    handleScroll();

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        hideFab();
      } else {
        setTimeout(handleScroll, 100);
      }
    });

    // Handle resize
    window.addEventListener('resize', () => {
      // Hide on desktop for scroll-top button
      if (type === 'scroll-top' && window.innerWidth >= 1024) {
        hideFab();
      } else {
        handleScroll();
      }
    });
  });
</script>

<style>
  .fab {
    /* Ensure proper stacking */
    z-index: 50;
    
    /* Smooth animations */
    will-change: transform, opacity;
    
    /* Better shadow for depth */
    box-shadow: 
      0 4px 14px 0 rgba(99, 102, 241, 0.3),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .fab:hover {
    box-shadow: 
      0 8px 25px 0 rgba(99, 102, 241, 0.4),
      0 4px 8px 0 rgba(0, 0, 0, 0.15);
  }

  .fab:active {
    box-shadow: 
      0 2px 8px 0 rgba(99, 102, 241, 0.3),
      0 1px 2px 0 rgba(0, 0, 0, 0.1);
  }

  /* Pulse animation for attention */
  @keyframes pulse-ring {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.3);
      opacity: 0;
    }
  }

  .fab::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    opacity: 0;
    animation: pulse-ring 2s infinite;
    z-index: -1;
  }

  /* Hide pulse on hover/focus */
  .fab:hover::before,
  .fab:focus::before {
    animation: none;
    opacity: 0;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .fab {
      transition: none;
    }
    
    .fab::before {
      animation: none;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .fab {
      border: 2px solid white;
    }
  }

  /* Print styles */
  @media print {
    .fab {
      display: none;
    }
  }
</style>