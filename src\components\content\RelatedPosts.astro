---
import PostCard from './PostCard.astro';
import type { CollectionEntry } from 'astro:content';

export interface Props {
  posts: CollectionEntry<'posts'>[];
  title?: string;
  class?: string;
}

const { posts, title = 'Related Posts', class: className } = Astro.props;

// Don't render if no posts
if (!posts || posts.length === 0) {
  return null;
}
---

<section class={`related-posts ${className || ''}`}>
  <div class="text-center mb-12">
    <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
      {title}
    </h2>
    <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
      Continue your learning journey with these hand-picked articles that complement this post.
    </p>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    {posts.map((post) => (
      <PostCard
        title={post.data.title}
        description={post.data.description}
        slug={post.id}
        publishDate={post.data.publishDate}
        tags={post.data.tags}
        category={post.data.category}
        coverImage={post.data.coverImage}
        readingTime={post.data.readingTime}

        featured={post.data.featured}
        difficulty={post.data.difficulty}
        class="h-full"
      />
    ))}
  </div>

  <!-- View More Link -->
  <div class="text-center mt-12">
    <a
      href="/blog"
      class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-200 font-medium"
    >
      <span class="mr-2">📚</span>
      View All Posts
    </a>
  </div>
</section>

<style>
  .related-posts {
    @apply py-16;
  }

  /* Animation for cards on load */
  .related-posts .grid > * {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  .related-posts .grid > *:nth-child(1) { animation-delay: 0.1s; }
  .related-posts .grid > *:nth-child(2) { animation-delay: 0.2s; }
  .related-posts .grid > *:nth-child(3) { animation-delay: 0.3s; }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .related-posts .grid > * {
      animation: none;
      opacity: 1;
      transform: none;
    }
  }
</style>