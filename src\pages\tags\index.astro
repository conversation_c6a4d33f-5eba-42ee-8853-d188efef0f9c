---
import { getCollection } from 'astro:content';
import PageLayout from '../../layouts/PageLayout.astro';
import Container from '../../components/layout/Container.astro';
import Section from '../../components/layout/Section.astro';
import Card from '../../components/ui/Card.astro';
import Button from '../../components/ui/Button.astro';
import { SITE } from '../../config/site';

const icons: Record<string, string> = {
  'ai-writing': '✍️',
  'thumbnails': '🎨',
  'video-automation': '🎬',
  'seo-for-creators': '📈'
};

const descriptions: Record<string, string> = {
  'AI Writing': 'Tools and techniques for AI-assisted writing and content generation.',
  'Thumbnails': 'AI-powered thumbnail creation and optimization strategies.',
  'Video Automation': 'Automated video editing and content repurposing workflows.',
  'SEO for Creators': 'AI-driven SEO optimization and content strategy.'
};

// Get all posts to extract tags
let allPosts: Array<{
  data: {
    tags: string[];
  };
}> = [];
try {
  allPosts = await getCollection('posts', ({ data }) => {
    return !data.draft;
  });
} catch (error) {
  // Collection doesn't exist or is empty
  allPosts = [];
}

// Extract and count tags
const tagCounts = new Map<string, number>();
allPosts.forEach((post: { data: { tags: string[] } }) => {
  post.data.tags.forEach((tag: string) => {
    tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
  });
});

// Sort tags by count (most used first)
const sortedTags = Array.from(tagCounts.entries())
  .sort(([, a], [, b]) => b - a)
  .map(([tag, count]) => ({ tag, count }));

const seo = {
  title: 'Tags',
  description: 'Browse all tags and topics covered on CreatorAITools. Find content about specific AI tools, techniques, and strategies for content creators.',
  canonical: '/tags',
};
---

<PageLayout {...seo}>
  <!-- Page Header -->
  <Section class="bg-gradient-to-br from-primary-50 to-violet-50 dark:from-gray-900 dark:to-gray-800">
    <Container>
      <div class="mx-auto max-w-3xl text-center py-12">
        <h1 class="text-fluid-4xl font-bold text-gray-900 dark:text-white mb-6">
          Browse by Tags
        </h1>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 mb-8">
          Explore content organized by topics, tools, and techniques. Find exactly what you're looking for.
        </p>
      </div>
    </Container>
  </Section>

  <!-- Categories Section -->
  <Section>
    <Container>
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Main Categories
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {SITE.categories.map((category) => {
            const slug = category.toLowerCase().replace(/\s+/g, '-');

            
            return (
              <Card hover class="text-center group h-full">
                <div class="text-4xl mb-4">{icons[slug] || '🚀'}</div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                  {category}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                  {descriptions[category]}
                </p>
                <Button 
                  href={`/tags/${slug}`}
                  variant="ghost"
                  size="sm"
                  class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                >
                  Explore →
                </Button>
              </Card>
            );
          })}
        </div>
      </div>
    </Container>
  </Section>

  <!-- All Tags Section -->
  {sortedTags.length > 0 ? (
    <Section class="bg-gray-50 dark:bg-gray-800/50">
      <Container>
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            All Tags ({sortedTags.length})
          </h2>
          <p class="text-gray-600 dark:text-gray-400">
            Click on any tag to see related posts and content.
          </p>
        </div>
        
        <!-- Tag Cloud -->
        <div class="flex flex-wrap gap-3">
          {sortedTags.map(({ tag, count }) => {
            const slug = tag.toLowerCase().replace(/\s+/g, '-');
            const sizeClass = count >= 5 ? 'text-lg' : count >= 3 ? 'text-base' : 'text-sm';
            
            return (
              <a
                href={`/tags/${slug}`}
                class={`
                  inline-flex items-center px-4 py-2 rounded-full
                  bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                  text-gray-700 dark:text-gray-300
                  hover:bg-primary-50 dark:hover:bg-primary-900/20
                  hover:border-primary-200 dark:hover:border-primary-700
                  hover:text-primary-700 dark:hover:text-primary-300
                  transition-all duration-200 transform hover:scale-105
                  ${sizeClass}
                `}
              >
                <span class="font-medium">{tag}</span>
                <span class="ml-2 px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 rounded-full">
                  {count}
                </span>
              </a>
            );
          })}
        </div>
      </Container>
    </Section>
  ) : (
    <!-- Empty State -->
    <Section class="bg-gray-50 dark:bg-gray-800/50">
      <Container>
        <div class="text-center py-16">
          <div class="mx-auto max-w-md">
            <div class="text-8xl mb-8">🏷️</div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              No Tags Yet
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
              Tags will appear here as we publish content. Each post will be tagged with relevant 
              topics, tools, and techniques to help you find exactly what you're looking for.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <Button href="/blog">
                Browse Content
              </Button>
              <Button href="/rss.xml" variant="outline">
                Subscribe for Updates
              </Button>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  )}

  <!-- Popular Searches -->
  <Section>
    <Container>
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Popular Searches
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-8">
          Common topics our readers are interested in.
        </p>
        
        <div class="flex flex-wrap justify-center gap-3">
          {[
            'ChatGPT', 'Midjourney', 'Canva AI', 'Jasper', 'Copy.ai',
            'Runway ML', 'Descript', 'Loom AI', 'Notion AI', 'Grammarly',
            'SEO Tools', 'Content Strategy', 'Video Editing', 'Thumbnail Design'
          ].map((term) => (
            <span class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full text-sm">
              {term}
            </span>
          ))}
        </div>
      </div>
    </Container>
  </Section>
</PageLayout>