---
import BaseLayout from '../layouts/BaseLayout.astro';
import Container from '../components/layout/Container.astro';
import GradientSection from '../components/layout/GradientSection.astro';
import { SITE } from '../config/site';

const seo = {
  title: '404 - Page Not Found | CreatorAITools',
  description: 'The page you are looking for could not be found. Explore our AI tools and resources for creators.',
  noindex: true,
};
---

<BaseLayout title={seo.title} description={seo.description} noindex={seo.noindex}>
  <GradientSection class="min-h-screen flex items-center">
    <Container>
      <div class="text-center max-w-2xl mx-auto">
        <!-- 404 Animation -->
        <div class="mb-8 relative">
          <div class="text-8xl md:text-9xl font-bold gradient-text animate-bounce-in">
            404
          </div>
          <div class="absolute inset-0 text-8xl md:text-9xl font-bold text-gray-200 dark:text-gray-800 -z-10 transform translate-x-1 translate-y-1">
            404
          </div>
        </div>

        <!-- Error Message -->
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Oops! Page Not Found
        </h1>
        
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
          The page you're looking for seems to have vanished into the digital void. 
          Don't worry though – we've got plenty of other great content to explore!
        </p>

        <!-- Search Box -->
        <div class="mb-8 max-w-md mx-auto">
          <div class="relative">
            <input
              type="search"
              placeholder="Search for content..."
              class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
              id="error-search"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center mb-12">
          <a
            href="/"
            class="btn-primary inline-flex items-center justify-center w-full sm:w-auto"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Go Home
          </a>

          <a
            href="/blog"
            class="btn-secondary inline-flex items-center justify-center w-full sm:w-auto"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            Browse Blog
          </a>

          <button
            onclick="history.back()"
            class="btn-secondary inline-flex items-center justify-center w-full sm:w-auto"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Go Back
          </button>
        </div>

        <!-- Popular Content -->
        <div class="text-left">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
            Popular Content
          </h2>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {SITE.navigation.slice(1, 5).map((item) => (
              <a
                href={item.href}
                class="group p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600 transition-all duration-200 hover:shadow-md card-interactive"
              >
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-100 to-violet-100 dark:from-indigo-900 dark:to-violet-900 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                      <svg class="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                      {item.name}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Explore {item.name.toLowerCase()} resources
                    </p>
                  </div>
                  <div class="flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-400 group-hover:text-indigo-500 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </a>
            ))}
          </div>
        </div>

        <!-- Categories -->
        <div class="mt-8 text-left">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
            Browse by Category
          </h3>
          
          <div class="flex flex-wrap justify-center gap-2">
            {SITE.categories.map((category) => (
              <a
                href={`/tags/${category.toLowerCase().replace(/\s+/g, '-')}`}
                class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-indigo-100 dark:hover:bg-indigo-900 hover:text-indigo-700 dark:hover:text-indigo-300 transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                {category}
              </a>
            ))}
          </div>
        </div>

        <!-- Help Text -->
        <div class="mt-12 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            <strong>Still can't find what you're looking for?</strong><br>
            Try using the search above or 
            <a href="/contact" class="text-indigo-600 dark:text-indigo-400 hover:underline">contact us</a> 
            for help.
          </p>
        </div>
      </div>
    </Container>
  </GradientSection>
</BaseLayout>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('error-search');
    
    if (searchInput) {
      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          const query = this.value.trim();
          if (query) {
            // Redirect to search or blog with query
            window.location.href = `/blog?search=${encodeURIComponent(query)}`;
          }
        }
      });

      // Auto-focus search on desktop
      if (window.innerWidth >= 768) {
        setTimeout(() => {
          searchInput.focus();
        }, 500);
      }
    }

    // Add staggered animation to popular content cards
    const cards = document.querySelectorAll('.card-interactive');
    cards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
      card.classList.add('animate-fade-in-up');
    });

    // Track 404 errors (for analytics)
    if (typeof gtag !== 'undefined') {
      gtag('event', 'page_view', {
        page_title: '404 Error',
        page_location: window.location.href,
        custom_map: { 'custom_parameter_1': 'error_page' }
      });
    }

    // Log referrer for debugging
    if (document.referrer) {
      console.log('404 - Referred from:', document.referrer);
    }
  });
</script>

<style>
  /* Custom animations for 404 page */
  .animate-bounce-in {
    animation: bounceIn 1s ease-out;
  }

  @keyframes bounceIn {
    0%, 20%, 40%, 60%, 80% {
      animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
      opacity: 0;
      transform: scale3d(.3, .3, .3);
    }
    20% {
      transform: scale3d(1.1, 1.1, 1.1);
    }
    40% {
      transform: scale3d(.9, .9, .9);
    }
    60% {
      opacity: 1;
      transform: scale3d(1.03, 1.03, 1.03);
    }
    80% {
      transform: scale3d(.97, .97, .97);
    }
    100% {
      opacity: 1;
      transform: scale3d(1, 1, 1);
    }
  }

  /* Ensure proper spacing on mobile */
  @media (max-width: 640px) {
    .space-y-4 > * + * {
      margin-top: 1rem;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .gradient-text {
      background: none;
      color: currentColor;
      -webkit-text-fill-color: currentColor;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .animate-bounce-in {
      animation: none;
    }
    
    .card-interactive {
      transform: none !important;
    }
  }
</style>