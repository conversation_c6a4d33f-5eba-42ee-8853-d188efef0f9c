---
export interface Props {
  title: string;
  description: string;
  slug: string;
  publishDate: Date;
  tags: string[];
  category: string;
  coverImage?: string;
  readingTime?: number;
  featured?: boolean;
  difficulty?: string;
  class?: string;
}

const {
  title,
  description,
  slug,
  publishDate,
  tags,
  category,
  coverImage,
  readingTime,
  featured = false,
  difficulty,
  class: className = '',
} = Astro.props;

const formattedDate = publishDate.toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
});


---

<article class={`post-card group ${className}`}>
  <a href={`/blog/${slug}`} class="block h-full">
    <!-- Cover Image -->
    {coverImage && (
      <div class="post-card-image">
        <img
          src={coverImage}
          alt={title}
          class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
      </div>
    )}
    
    <!-- Content -->
    <div class="post-card-content">
      <!-- Category Badge -->
      <div class="mb-3">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
          {category}
        </span>
      </div>
      
      <!-- Title -->
      <h3 class="post-card-title group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200">
        {title}
      </h3>
      
      <!-- Description -->
      <p class="post-card-description">
        {description}
      </p>
      
      <!-- Meta Information -->
      <div class="post-card-meta">
        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
          <time datetime={publishDate.toISOString()}>
            {formattedDate}
          </time>
          {readingTime && (
            <>
              <span class="mx-2">•</span>
              <span>{readingTime} min read</span>
            </>
          )}
        </div>
        
        <!-- Tags -->
        {tags.length > 0 && (
          <div class="flex flex-wrap gap-1 mt-2">
            {tags.slice(0, 3).map((tag) => (
              <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300">
                {tag}
              </span>
            ))}
            {tags.length > 3 && (
              <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300">
                +{tags.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  </a>
</article>

<style>
  .post-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    border: 1px solid rgb(229 231 235);
    overflow: hidden;
    transition: all 0.3s ease-out;
    transform: translateY(0);
  }

  [data-theme="dark"] .post-card {
    background-color: rgb(31 41 55);
    border-color: rgb(55 65 81);
  }

  .post-card:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-color: rgb(199 210 254);
    transform: translateY(-0.25rem);
  }

  [data-theme="dark"] .post-card:hover {
    border-color: rgb(67 56 202);
  }

  .post-card-image {
    position: relative;
    overflow: hidden;
  }

  .post-card-content {
    padding: 1.5rem;
  }

  .post-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgb(17 24 39);
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  [data-theme="dark"] .post-card-title {
    color: white;
  }

  .post-card-description {
    color: rgb(75 85 99);
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.625;
  }

  [data-theme="dark"] .post-card-description {
    color: rgb(209 213 219);
  }

  .post-card-meta {
    margin-top: auto;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .post-card-content {
      padding: 1rem;
    }

    .post-card-title {
      font-size: 1.125rem;
    }

    .post-card-description {
      font-size: 0.875rem;
    }
  }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Focus styles for accessibility */
  .post-card a:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgb(99 102 241), 0 0 0 4px rgb(99 102 241 / 0.2);
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .post-card {
      transition: none;
      transform: none;
    }

    .post-card img {
      transition: none;
      transform: none;
    }
  }
</style>