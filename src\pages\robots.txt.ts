import type { APIRoute } from 'astro';
import { SITE } from '../config/site';

export const GET: APIRoute = () => {
  const robotsTxt = `
# CreatorAITools - Robots.txt
# Generated automatically by Astro

User-agent: *
Allow: /

# Sitemaps
Sitemap: ${SITE.url}/sitemap-index.xml
Sitemap: ${SITE.url}/rss.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas (if any)
Disallow: /admin/
Disallow: /private/
Disallow: /_astro/
Disallow: /api/

# Allow all content for SEO
Allow: /blog/
Allow: /tags/
Allow: /about
Allow: /contact
Allow: /backlinks

# Performance optimization hints
# Allow crawling of CSS and JS for better rendering
Allow: /*.css$
Allow: /*.js$

# Block common bot traps
Disallow: /trap/
Disallow: /honeypot/

# Mobile-first indexing support
# No specific mobile restrictions

# Social media and search engine specific rules
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: facebookexternalhit
Allow: /

User-agent: LinkedInBot
Allow: /

# Block aggressive crawlers
User-agent: AhrefsBot
Crawl-delay: 10

User-agent: SemrushBot
Crawl-delay: 10

User-agent: MJ12bot
Crawl-delay: 10

# Block AI training crawlers (optional - uncomment if desired)
# User-agent: GPTBot
# Disallow: /

# User-agent: ChatGPT-User
# Disallow: /

# User-agent: CCBot
# Disallow: /

# User-agent: anthropic-ai
# Disallow: /

# User-agent: Claude-Web
# Disallow: /
`.trim();

  return new Response(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    },
  });
};