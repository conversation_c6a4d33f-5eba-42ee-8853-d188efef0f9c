---
import { Image } from 'astro:assets';

export interface Props {
  src: string | ImageMetadata;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  sizes?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg';
  aspectRatio?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  placeholder?: 'blur' | 'color' | 'none';
  placeholderColor?: string;
  caption?: string;
  priority?: boolean;
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  decoding = 'async',
  sizes,
  quality = 80,
  format = 'webp',
  aspectRatio,
  objectFit = 'cover',
  placeholder = 'blur',
  placeholderColor = '#f3f4f6',
  caption,
  priority = false,
} = Astro.props;

// Generate responsive sizes if not provided
const defaultSizes = sizes || '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';

// Determine loading strategy
const imageLoading = priority ? 'eager' : loading;

// Generate placeholder for better UX
const placeholderSvg = `data:image/svg+xml;base64,${btoa(`
  <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${placeholderColor}"/>
    <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="system-ui" font-size="14">
      Loading...
    </text>
  </svg>
`)}`;
---

<figure class={`optimized-image-container ${className}`}>
  <div 
    class="optimized-image-wrapper"
    style={aspectRatio ? `aspect-ratio: ${aspectRatio}` : undefined}
  >
    {placeholder === 'blur' && (
      <div class="optimized-image-placeholder">
        <img
          src={placeholderSvg}
          alt=""
          class="optimized-image-blur"
          aria-hidden="true"
        />
      </div>
    )}
    
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      loading={imageLoading}
      decoding={decoding}
      sizes={defaultSizes}
      quality={quality}
      format={format}
      class={`optimized-image ${objectFit ? `object-${objectFit}` : ''}`}
      onload="this.parentElement.classList.add('loaded')"
      onerror="this.parentElement.classList.add('error')"
    />
    
    {/* Error fallback */}
    <div class="optimized-image-error">
      <svg class="w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      <p class="text-sm text-gray-500 mt-2">Image failed to load</p>
    </div>
  </div>
  
  {caption && (
    <figcaption class="optimized-image-caption">
      {caption}
    </figcaption>
  )}
</figure>

<style>
  .optimized-image-container {
    @apply relative;
  }

  .optimized-image-wrapper {
    @apply relative overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800;
    @apply transition-all duration-300 ease-out;
  }

  .optimized-image {
    @apply w-full h-full transition-all duration-500 ease-out;
    @apply opacity-0;
  }

  .optimized-image-wrapper.loaded .optimized-image {
    @apply opacity-100;
  }

  .optimized-image-wrapper.loaded .optimized-image-placeholder {
    @apply opacity-0;
  }

  .optimized-image-placeholder {
    @apply absolute inset-0 transition-opacity duration-500 ease-out;
  }

  .optimized-image-blur {
    @apply w-full h-full object-cover filter blur-sm scale-110;
  }

  .optimized-image-error {
    @apply absolute inset-0 flex flex-col items-center justify-center;
    @apply bg-gray-50 dark:bg-gray-800;
    @apply opacity-0 transition-opacity duration-300;
  }

  .optimized-image-wrapper.error .optimized-image-error {
    @apply opacity-100;
  }

  .optimized-image-wrapper.error .optimized-image {
    @apply opacity-0;
  }

  .optimized-image-caption {
    @apply mt-2 text-sm text-gray-600 dark:text-gray-400 text-center;
    @apply leading-relaxed;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .optimized-image-wrapper {
      @apply rounded-lg; /* Slightly smaller radius on mobile */
    }

    .optimized-image-caption {
      @apply text-xs px-2;
    }
  }

  /* Hover effects for interactive images */
  .optimized-image-container.interactive .optimized-image-wrapper {
    @apply cursor-pointer;
    @apply hover:shadow-lg hover:scale-105;
    @apply focus-within:shadow-lg focus-within:scale-105;
  }

  /* Loading animation */
  .optimized-image-wrapper:not(.loaded):not(.error) .optimized-image-placeholder::after {
    content: '';
    @apply absolute inset-0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }

  [data-theme="dark"] .optimized-image-wrapper:not(.loaded):not(.error) .optimized-image-placeholder::after {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* Intersection Observer enhancement */
  .optimized-image-container[data-aos] {
    @apply opacity-0 transform translate-y-4;
    @apply transition-all duration-600 ease-out;
  }

  .optimized-image-container[data-aos].aos-animate {
    @apply opacity-100 transform translate-y-0;
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .optimized-image-wrapper {
      @apply border-2 border-gray-900 dark:border-gray-100;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .optimized-image,
    .optimized-image-placeholder,
    .optimized-image-wrapper {
      @apply transition-none;
    }

    .optimized-image-wrapper:not(.loaded):not(.error) .optimized-image-placeholder::after {
      animation: none;
    }
  }

  /* Print styles */
  @media print {
    .optimized-image-wrapper {
      @apply shadow-none;
    }

    .optimized-image-placeholder {
      @apply hidden;
    }

    .optimized-image {
      @apply opacity-100;
    }
  }
</style>

<script>
  // Intersection Observer for lazy loading enhancement
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const container = entry.target as HTMLElement;
          container.setAttribute('data-aos', 'fade-up');
          
          // Add animation class after a small delay
          setTimeout(() => {
            container.classList.add('aos-animate');
          }, 100);
          
          imageObserver.unobserve(container);
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.1
    });

    // Observe all image containers
    document.querySelectorAll('.optimized-image-container').forEach((container) => {
      imageObserver.observe(container);
    });
  }

  // Handle image load events
  document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('.optimized-image');
    
    images.forEach((img) => {
      const wrapper = img.closest('.optimized-image-wrapper');
      
      img.addEventListener('load', () => {
        wrapper?.classList.add('loaded');
        
        // Haptic feedback on mobile for interactive images
        if ('vibrate' in navigator && wrapper?.closest('.interactive')) {
          navigator.vibrate(25);
        }
      });
      
      img.addEventListener('error', () => {
        wrapper?.classList.add('error');
      });
      
      // If image is already loaded (cached)
      if (img.complete && img.naturalHeight !== 0) {
        wrapper?.classList.add('loaded');
      }
    });
  });
</script>