---
import { SITE } from '../config/site';
import { generateSEO, generateStructuredData } from '../config/seo';
import ConsentBanner from '../components/ads/ConsentBanner.astro';
import '../styles/global.css';

export interface Props {
  title?: string;
  description?: string;
  canonical?: string;
  image?: string;
  imageAlt?: string;
  noindex?: boolean;
  nofollow?: boolean;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
  class?: string;
}

const {
  title,
  description,
  canonical,
  image,
  imageAlt,
  noindex = false,
  nofollow = false,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  tags,
  class: className,
} = Astro.props;

const seo = generateSEO({
  title,
  description,
  canonical,
  image,
  imageAlt,
  noindex,
  nofollow,
  type,
  publishedTime,
  modifiedTime,
  author,
  tags,
});

const structuredData = generateStructuredData(seo, type);
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <!-- Essential Meta Tags -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta name="generator" content={Astro.generator} />

    <!-- SEO Meta Tags -->
    <title>{seo.title}</title>
    <meta name="description" content={seo.description} />
    <link rel="canonical" href={seo.canonical} />

    <!-- Robots Meta -->
    {
      (seo.noindex || seo.nofollow) && (
        <meta
          name="robots"
          content={`${seo.noindex ? 'noindex' : 'index'},${seo.nofollow ? 'nofollow' : 'follow'}`}
        />
      )
    }

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content={seo.type} />
    <meta property="og:title" content={seo.title} />
    <meta property="og:description" content={seo.description} />
    <meta property="og:url" content={seo.canonical} />
    <meta property="og:image" content={seo.image} />
    <meta property="og:image:alt" content={seo.imageAlt} />
    <meta property="og:site_name" content={SITE.name} />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={seo.title} />
    <meta name="twitter:description" content={seo.description} />
    <meta name="twitter:image" content={seo.image} />
    <meta name="twitter:image:alt" content={seo.imageAlt} />

    <!-- Article Meta Tags (for blog posts) -->
    {
      type === 'article' && (
        <>
          {seo.publishedTime && (
            <meta property="article:published_time" content={seo.publishedTime} />
          )}
          {seo.modifiedTime && <meta property="article:modified_time" content={seo.modifiedTime} />}
          {seo.author && <meta property="article:author" content={seo.author} />}
          {seo.tags && seo.tags.map((tag) => <meta property="article:tag" content={tag} />)}
        </>
      )
    }

    <!-- Favicons and App Icons -->
    <link rel="icon" type="image/svg+xml" href="/creatoraitools/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/creatoraitools/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/creatoraitools/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="180x180" href="/creatoraitools/apple-touch-icon.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/creatoraitools/manifest.json" />
    <meta name="theme-color" content="#6366f1" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Font Loading with display: swap for performance -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Structured Data -->
    <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />

    <!-- Performance Monitoring -->
    <script src="/creatoraitools/performance-monitor.js" async></script>

    <!-- Theme Detection Script (inline for no FOUC) -->
    <script is:inline>
      // Theme detection and application
      (function () {
        const getTheme = () => {
          if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
            return localStorage.getItem('theme');
          }
          if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
          }
          return 'light';
        };

        const theme = getTheme();
        document.documentElement.setAttribute('data-theme', theme);

        // Add theme class for Tailwind
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
  </head>

  <body
    class={`min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased ${className || ''}`}
  >
    <!-- Skip Link for Accessibility -->
    <a
      href="#main-content"
      class="skip-link sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded-md focus:shadow-lg"
    >
      Skip to main content
    </a>

    <!-- Page Content -->
    <div class="flex min-h-screen flex-col">
      <slot />
    </div>

    <!-- Consent Banner (only shows if ads are enabled and consent required) -->
    <ConsentBanner />

    <!-- Service Worker Registration (for PWA) -->
    <script is:inline>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', async () => {
          try {
            const registration = await navigator.serviceWorker.register('/creatoraitools/sw.js', {
              scope: '/creatoraitools/'
            });
            
            console.log('Service Worker registered successfully:', registration);
            
            // Handle service worker updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New service worker is available, show update notification
                    showUpdateNotification();
                  }
                });
              }
            });
            
            // Listen for messages from service worker
            navigator.serviceWorker.addEventListener('message', (event) => {
              if (event.data && event.data.type === 'CACHE_UPDATED') {
                console.log('Cache updated:', event.data.url);
              }
            });
            
          } catch (error) {
            console.log('Service Worker registration failed:', error);
          }
        });
      }

      // Show update notification
      function showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-indigo-600 text-white p-4 rounded-lg shadow-lg z-50 animate-slide-in-right';
        notification.innerHTML = `
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium">Update Available</p>
              <p class="text-xs opacity-90 mt-1">A new version is ready to install.</p>
            </div>
            <button onclick="updateServiceWorker()" class="ml-4 px-3 py-1 bg-white/20 hover:bg-white/30 rounded text-xs font-medium transition-colors">
              Update
            </button>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 p-1 hover:bg-white/20 rounded transition-colors">
              <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        `;
        document.body.appendChild(notification);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (notification.parentElement) {
            notification.remove();
          }
        }, 10000);
      }

      // Update service worker
      function updateServiceWorker() {
        if (navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' });
          window.location.reload();
        }
      }

      // Install prompt for PWA
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        
        // Show install button after a delay
        setTimeout(() => {
          showInstallPrompt();
        }, 30000); // Show after 30 seconds
      });

      function showInstallPrompt() {
        if (!deferredPrompt) return;
        
        const installPrompt = document.createElement('div');
        installPrompt.className = 'fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg shadow-lg z-50 animate-slide-in-right';
        installPrompt.innerHTML = `
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">Install CreatorAITools</p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Get quick access and offline reading</p>
              <div class="flex space-x-2 mt-3">
                <button onclick="installPWA()" class="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded transition-colors">
                  Install
                </button>
                <button onclick="this.closest('div[class*=\"fixed\"]').remove()" class="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs font-medium rounded transition-colors">
                  Not now
                </button>
              </div>
            </div>
          </div>
        `;
        document.body.appendChild(installPrompt);
      }

      async function installPWA() {
        if (!deferredPrompt) return;
        
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        if (outcome === 'accepted') {
          console.log('PWA installed');
        }
        
        deferredPrompt = null;
        document.querySelector('div[class*="fixed"]')?.remove();
      }

      // Handle app installed event
      window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        deferredPrompt = null;
      });

      // Performance monitoring
      if ('performance' in window && 'PerformanceObserver' in window) {
        // Monitor Core Web Vitals
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'largest-contentful-paint') {
              console.log('LCP:', entry.startTime);
            }
            if (entry.entryType === 'first-input') {
              console.log('FID:', entry.processingStart - entry.startTime);
            }
            if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
              console.log('CLS:', entry.value);
            }
          }
        });
        
        try {
          observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
        } catch (e) {
          // Observer not supported
        }
      }
    </script>
  </body>
</html>

<style>
  /* Critical CSS for above-the-fold content */
  html {
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  /* Ensure proper touch targets on mobile */
  @media (max-width: 768px) {
    button,
    a[role='button'],
    input,
    select,
    textarea {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Skip link styles */
  .skip-link {
    transform: translateY(-100%);
    transition: transform 0.3s;
  }

  .skip-link:focus {
    transform: translateY(0%);
  }
</style>
