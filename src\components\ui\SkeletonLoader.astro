---
export interface Props {
  variant?: 'post-card' | 'post-list' | 'text' | 'avatar' | 'button' | 'image';
  count?: number;
  class?: string;
}

const { variant = 'text', count = 1, class: className } = Astro.props;
---

<div class={`skeleton-container ${className || ''}`}>
  {Array.from({ length: count }).map((_, index) => (
    <div key={index} class={`skeleton skeleton-${variant}`}>
      {variant === 'post-card' && (
        <div class="space-y-4">
          <!-- Image placeholder -->
          <div class="skeleton-image aspect-video rounded-lg"></div>
          <!-- Content -->
          <div class="space-y-2">
            <div class="skeleton-text h-6 w-3/4"></div>
            <div class="skeleton-text h-4 w-full"></div>
            <div class="skeleton-text h-4 w-2/3"></div>
          </div>
          <!-- Meta -->
          <div class="flex items-center space-x-4">
            <div class="skeleton-avatar w-8 h-8 rounded-full"></div>
            <div class="skeleton-text h-4 w-24"></div>
            <div class="skeleton-text h-4 w-16"></div>
          </div>
        </div>
      )}

      {variant === 'post-list' && (
        <div class="flex space-x-4">
          <div class="skeleton-image w-20 h-20 rounded-lg flex-shrink-0"></div>
          <div class="flex-1 space-y-2">
            <div class="skeleton-text h-5 w-3/4"></div>
            <div class="skeleton-text h-4 w-full"></div>
            <div class="skeleton-text h-3 w-1/2"></div>
          </div>
        </div>
      )}

      {variant === 'text' && (
        <div class="skeleton-text h-4 w-full"></div>
      )}

      {variant === 'avatar' && (
        <div class="skeleton-avatar w-10 h-10 rounded-full"></div>
      )}

      {variant === 'button' && (
        <div class="skeleton-button h-10 w-24 rounded-lg"></div>
      )}

      {variant === 'image' && (
        <div class="skeleton-image aspect-video rounded-lg"></div>
      )}
    </div>
  ))}
</div>

<style>
  .skeleton-container {
    @apply space-y-4;
  }

  .skeleton {
    @apply animate-pulse;
  }

  .skeleton-text {
    @apply bg-gray-200 dark:bg-gray-700 rounded;
  }

  .skeleton-image {
    @apply bg-gray-200 dark:bg-gray-700 rounded-lg;
  }

  .skeleton-avatar {
    @apply bg-gray-200 dark:bg-gray-700 rounded-full;
  }

  .skeleton-button {
    @apply bg-gray-200 dark:bg-gray-700 rounded-lg;
  }

  /* Shimmer effect for better visual feedback */
  .skeleton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }

  [data-theme="dark"] .skeleton::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }

  .skeleton {
    position: relative;
    overflow: hidden;
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .skeleton {
      animation: none;
    }
    
    .skeleton::before {
      animation: none;
    }
  }
</style>