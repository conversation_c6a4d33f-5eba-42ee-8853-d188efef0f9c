---
import PageLayout from '../layouts/PageLayout.astro';
import Container from '../components/layout/Container.astro';
import Section from '../components/layout/Section.astro';
import GradientSection from '../components/layout/GradientSection.astro';
import Card from '../components/ui/Card.astro';
import Button from '../components/ui/Button.astro';
import { SITE } from '../config/site';

const seo = {
  title: 'About Us',
  description: 'Learn about CreatorAITools and our mission to help content creators leverage AI tools effectively for writing, thumbnails, video automation, and SEO.',
  canonical: '/about',
};
---

<PageLayout {...seo}>
  <!-- Hero Section -->
  <GradientSection variant="subtle" class="relative">
    <Section padding="xl">
      <Container>
        <div class="mx-auto max-w-4xl text-center">
          <h1 class="text-fluid-4xl font-bold text-gray-900 dark:text-white mb-6">
            About <span class="gradient-text">CreatorAITools</span>
          </h1>
          <p class="text-fluid-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            {SITE.tagline}
          </p>
          <p class="text-fluid-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            We're passionate about helping content creators harness the power of AI to enhance their creative workflow, 
            increase productivity, and create better content faster.
          </p>
        </div>
      </Container>
    </Section>
  </GradientSection>

  <!-- Mission Section -->
  <Section>
    <Container>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h2 class="text-fluid-3xl font-bold text-gray-900 dark:text-white mb-6">
            Our Mission
          </h2>
          <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
            The AI revolution is transforming content creation, but many creators struggle to navigate 
            the overwhelming number of tools and techniques available. We bridge that gap by providing 
            practical, actionable guidance.
          </p>
          <p class="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
            Our micro-niche approach focuses on specific use cases and workflows, giving you exactly 
            what you need to implement AI tools effectively in your creative process.
          </p>
          <Button href="/blog" class="inline-flex items-center">
            Explore Our Content
            <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </Button>
        </div>
        
        <div class="relative">
          <div class="aspect-square bg-gradient-to-br from-indigo-100 to-violet-100 dark:from-indigo-900 dark:to-violet-900 rounded-2xl flex items-center justify-center">
            <div class="text-6xl">🚀</div>
          </div>
        </div>
      </div>
    </Container>
  </Section>

  <!-- What We Cover Section -->
  <Section class="bg-gray-50 dark:bg-gray-800/50">
    <Container>
      <div class="text-center mb-12">
        <h2 class="text-fluid-3xl font-bold text-gray-900 dark:text-white mb-4">
          What We Cover
        </h2>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Our content spans the most impactful AI applications for content creators.
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {SITE.categories.map((category) => {
          const descriptions = {
            'AI Writing': 'Tools and techniques for AI-assisted writing, editing, and content generation.',
            'Thumbnails': 'AI-powered thumbnail creation, optimization, and A/B testing strategies.',
            'Video Automation': 'Automated video editing, transcription, and content repurposing workflows.',
            'SEO for Creators': 'AI-driven SEO optimization, keyword research, and content strategy.'
          };
          
          const icons = {
            'AI Writing': '✍️',
            'Thumbnails': '🎨',
            'Video Automation': '🎬',
            'SEO for Creators': '📈'
          };
          
          return (
            <Card hover class="text-center h-full">
              <div class="text-4xl mb-4">{icons[category] || '🚀'}</div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                {category}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                {descriptions[category]}
              </p>
            </Card>
          );
        })}
      </div>
    </Container>
  </Section>

  <!-- Values Section -->
  <Section>
    <Container>
      <div class="text-center mb-12">
        <h2 class="text-fluid-3xl font-bold text-gray-900 dark:text-white mb-4">
          Our Values
        </h2>
        <p class="text-fluid-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          The principles that guide everything we do.
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl text-white">🎯</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            Practical Focus
          </h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
            We focus on actionable strategies and real-world applications, not theoretical concepts.
          </p>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl text-white">🔍</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            Quality Over Quantity
          </h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
            We carefully curate and test every tool and technique before recommending it.
          </p>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl text-white">🤝</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            Community First
          </h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
            We're building a community of creators who support and learn from each other.
          </p>
        </div>
      </div>
    </Container>
  </Section>

  <!-- CTA Section -->
  <Section class="bg-gradient-to-r from-indigo-500 to-violet-500">
    <Container>
      <div class="text-center text-white">
        <h2 class="text-fluid-3xl font-bold mb-4">
          Ready to Level Up Your Content?
        </h2>
        <p class="text-fluid-lg mb-8 opacity-90 max-w-2xl mx-auto">
          Join thousands of creators who are already using AI to enhance their workflow and create better content.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            href="/blog"
            class="bg-white text-indigo-600 hover:bg-gray-100 focus:ring-white"
          >
            Start Learning
          </Button>
          <Button 
            href="/contact"
            variant="outline"
            class="border-white/30 text-white hover:bg-white/10 focus:ring-white"
          >
            Get in Touch
          </Button>
        </div>
      </div>
    </Container>
  </Section>
</PageLayout>