export const ADS_CONFIG = {
  enabled: false, // Set to true to enable ads

  // PropellerAds configuration
  propellerAds: {
    // Replace with your actual PropellerAds IDs when ready
    publisherId: 'YOUR_PUBLISHER_ID',

    // Ad unit configurations
    units: {
      inPagePush: {
        id: 'YOUR_IN_PAGE_PUSH_ID',
        enabled: false,
      },
      sidebar: {
        id: 'YOUR_SIDEBAR_AD_ID',
        enabled: false,
      },
      footer: {
        id: 'YOUR_FOOTER_AD_ID',
        enabled: false,
      },
      inArticle: {
        id: 'YOUR_IN_ARTICLE_AD_ID',
        enabled: false,
      },
      banner: {
        id: 'YOUR_BANNER_AD_ID',
        enabled: false,
      },
      interstitial: {
        id: 'YOUR_INTERSTITIAL_AD_ID',
        enabled: false,
      },
      native: {
        id: 'YOUR_NATIVE_AD_ID',
        enabled: false,
      },
    },
  },

  // Privacy and consent settings
  privacy: {
    requireConsent: true, // Set to true for GDPR compliance
    consentCookieName: 'ads-consent',
    consentDuration: 365, // days
  },

  // Performance settings
  performance: {
    lazyLoad: true, // Lazy load ads
    deferLoad: true, // Defer ad loading until after page load
    respectReducedMotion: true, // Respect prefers-reduced-motion
  },
} as const;

export type AdsConfig = typeof ADS_CONFIG;
