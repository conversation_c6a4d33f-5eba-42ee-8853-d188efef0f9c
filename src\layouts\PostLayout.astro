---
import BaseLayout from './BaseLayout.astro';
import Header from '../components/ui/Header.astro';
import Footer from '../components/ui/Footer.astro';
import Container from '../components/layout/Container.astro';
import Section from '../components/layout/Section.astro';
import TableOfContents from '../components/content/TableOfContents.astro';
import ShareButtons from '../components/content/ShareButtons.astro';
import FloatingActionButton from '../components/ui/FloatingActionButton.astro';
import type { MarkdownHeading } from 'astro';

export interface Props {
  title: string;
  description: string;
  publishDate: Date;
  updatedDate?: Date;
  author: string;
  tags: string[];
  category: string;
  readingTime?: number;
  wordCount?: number;
  headings: MarkdownHeading[];
  coverImage?: string;
  coverImageAlt?: string;
}

const {
  title,
  description,
  publishDate,
  updatedDate,
  author,
  tags,
  category,
  readingTime,
  wordCount,
  headings,
  coverImage,
  coverImageAlt,
} = Astro.props;

const seo = {
  title,
  description,
  type: 'article' as const,
  publishedTime: publishDate.toISOString(),
  modifiedTime: updatedDate?.toISOString(),
  author,
  tags,
  image: coverImage,
  imageAlt: coverImageAlt,
};

const formattedDate = publishDate.toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
});

const categorySlug = category.toLowerCase().replace(/\s+/g, '-');
---

<BaseLayout {...seo}>
  <Header />
  
  <main id="main-content" class="flex-1">
    <!-- Article Header -->
    <Section class="bg-gradient-to-br from-gray-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800">
      <Container>
        <div class="max-w-4xl mx-auto py-12 lg:py-16">
          <!-- Breadcrumbs -->
          <nav class="mb-8" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <li><a href="/" class="hover:text-indigo-600 dark:hover:text-indigo-400">Home</a></li>
              <li class="text-gray-300 dark:text-gray-600">/</li>
              <li><a href="/blog" class="hover:text-indigo-600 dark:hover:text-indigo-400">Blog</a></li>
              <li class="text-gray-300 dark:text-gray-600">/</li>
              <li><a href={`/tags/${categorySlug}`} class="hover:text-indigo-600 dark:hover:text-indigo-400">{category}</a></li>
            </ol>
          </nav>

          <!-- Category Badge -->
          <div class="mb-4">
            <a 
              href={`/tags/${categorySlug}`}
              class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors"
            >
              {category}
            </a>
          </div>

          <!-- Title -->
          <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
            {title}
          </h1>

          <!-- Description -->
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            {description}
          </p>

          <!-- Article Meta -->
          <div class="flex flex-wrap items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>By {author}</span>
            </div>
            
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <time datetime={publishDate.toISOString()}>{formattedDate}</time>
            </div>

            {readingTime && (
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{readingTime} min read</span>
              </div>
            )}

            {wordCount && (
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>{wordCount.toLocaleString()} words</span>
              </div>
            )}
          </div>

          <!-- Share Buttons -->
          <div class="mt-8">
            <ShareButtons 
              title={title}
              url={Astro.url.pathname}
              description={description}
            />
          </div>
        </div>
      </Container>
    </Section>

    <!-- Article Content -->
    <Section>
      <Container>
        <div class="max-w-6xl mx-auto">
          <div class="lg:grid lg:grid-cols-12 lg:gap-8">
            <!-- Table of Contents (Desktop Sidebar) -->
            <aside class="hidden lg:block lg:col-span-3 xl:col-span-2">
              <div class="sticky top-24">
                <TableOfContents headings={headings} />
              </div>
            </aside>

            <!-- Main Content -->
            <article class="lg:col-span-9 xl:col-span-8">
              <!-- Mobile TOC -->
              <div class="lg:hidden mb-8">
                <TableOfContents headings={headings} mobile />
              </div>

              <!-- Cover Image -->
              {coverImage && (
                <div class="mb-8">
                  <img
                    src={coverImage}
                    alt={coverImageAlt || title}
                    class="w-full h-64 md:h-96 object-cover rounded-xl shadow-lg"
                    loading="eager"
                  />
                </div>
              )}

              <!-- Article Body -->
              <div class="prose prose-lg dark:prose-invert max-w-none">
                <slot />
              </div>

              <!-- Article Footer -->
              <footer class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                <!-- Updated Date -->
                {updatedDate && updatedDate.getTime() !== publishDate.getTime() && (
                  <div class="mb-6 text-sm text-gray-500 dark:text-gray-400">
                    <span>Last updated: </span>
                    <time datetime={updatedDate.toISOString()}>
                      {updatedDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </time>
                  </div>
                )}

                <!-- Tags -->
                <div class="mb-8">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tags</h3>
                  <div class="flex flex-wrap gap-2">
                    {tags.map((tag) => {
                      const tagSlug = tag.toLowerCase().replace(/\s+/g, '-');
                      return (
                        <a
                          href={`/tags/${tagSlug}`}
                          class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700 hover:bg-indigo-100 hover:text-indigo-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-indigo-900 dark:hover:text-indigo-300 transition-colors"
                        >
                          #{tag}
                        </a>
                      );
                    })}
                  </div>
                </div>

                <!-- Share Again -->
                <div class="mb-8">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Share this article</h3>
                  <ShareButtons 
                    title={title}
                    url={Astro.url.pathname}
                    description={description}
                  />
                </div>
              </footer>
            </article>

            <!-- Right Sidebar (Desktop) -->
            <aside class="hidden xl:block xl:col-span-2">
              <div class="sticky top-24 space-y-6">
                <!-- Author Info -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">About the Author</h3>
                  <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-violet-500 rounded-full flex items-center justify-center text-white font-bold">
                      {author.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div class="ml-3">
                      <p class="font-medium text-gray-900 dark:text-white">{author}</p>
                      <p class="text-sm text-gray-500 dark:text-gray-400">Content Creator</p>
                    </div>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-300">
                    Expert in AI tools and content creation strategies. Helping creators leverage technology for better results.
                  </p>
                </div>

                <!-- Related Categories -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Explore More</h3>
                  <div class="space-y-2">
                    <a href="/blog" class="block text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                      All Articles
                    </a>
                    <a href={`/tags/${categorySlug}`} class="block text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                      More {category}
                    </a>
                    <a href="/tags" class="block text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                      Browse by Topic
                    </a>
                  </div>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </Container>
    </Section>
  </main>
  
  <Footer />
  
  <!-- Floating Action Buttons -->
  <FloatingActionButton type="scroll-top" position="bottom-right" />
  <FloatingActionButton type="share" position="bottom-left" />
</BaseLayout>

<style>
  /* Article-specific prose styles */
  .prose {
    @apply text-gray-900 dark:text-gray-100;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply text-gray-900 dark:text-white;
    @apply scroll-mt-24; /* Account for sticky header */
  }

  .prose a {
    @apply text-indigo-600 dark:text-indigo-400;
    @apply no-underline hover:underline;
  }

  .prose code {
    @apply bg-gray-100 dark:bg-gray-800;
    @apply text-gray-900 dark:text-gray-100;
    @apply px-1.5 py-0.5 rounded;
  }

  .prose pre {
    @apply bg-gray-900 dark:bg-gray-950;
    @apply border border-gray-700;
  }

  .prose blockquote {
    @apply border-l-4 border-indigo-500;
    @apply bg-indigo-50 dark:bg-indigo-900/20;
    @apply text-gray-900 dark:text-gray-100;
  }

  .prose img {
    @apply rounded-lg shadow-md;
  }

  .prose table {
    @apply border border-gray-200 dark:border-gray-700;
  }

  .prose th,
  .prose td {
    @apply border border-gray-200 dark:border-gray-700;
  }

  .prose th {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .prose {
      @apply text-base;
    }

    .prose h1 {
      @apply text-2xl;
    }

    .prose h2 {
      @apply text-xl;
    }

    .prose h3 {
      @apply text-lg;
    }
  }
</style>