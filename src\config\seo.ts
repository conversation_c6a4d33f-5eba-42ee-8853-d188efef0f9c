import { SITE } from './site';

export interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  image?: string;
  imageAlt?: string;
  noindex?: boolean;
  nofollow?: boolean;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

export function generateSEO(props: SEOProps = {}) {
  const {
    title,
    description = SITE.description,
    canonical,
    image = SITE.defaultOgImage,
    imageAlt = SITE.title,
    noindex = false,
    nofollow = false,
    type = 'website',
    publishedTime,
    modifiedTime,
    author = SITE.author,
    tags = [],
  } = props;

  // Validate required fields for SEO
  if (title && title.trim().length === 0) {
    throw new Error('SEO title cannot be empty');
  }

  if (description && description.trim().length === 0) {
    throw new Error('SEO description cannot be empty');
  }

  // SEO length warnings
  if (title && title.length > 60) {
    console.warn(`SEO Warning: Title "${title}" is ${title.length} characters (recommended: ≤60)`);
  }

  if (description && description.length > 160) {
    console.warn(`SEO Warning: Description is ${description.length} characters (recommended: ≤160)`);
  }

  // Generate full title
  const fullTitle = title ? `${title} | ${SITE.name}` : SITE.title;

  // Generate canonical URL
  const canonicalUrl = canonical ? `${SITE.url}${canonical}` : SITE.url;

  // Generate full image URL
  const fullImageUrl = image.startsWith('http') ? image : `${SITE.url}${image}`;

  return {
    title: fullTitle,
    description,
    canonical: canonicalUrl,
    image: fullImageUrl,
    imageAlt,
    noindex,
    nofollow,
    type,
    publishedTime,
    modifiedTime,
    author,
    tags,
    siteName: SITE.name,
    siteUrl: SITE.url,
  };
}

export function generateStructuredData(
  seo: ReturnType<typeof generateSEO>,
  pageType: 'website' | 'article' | 'organization' = 'website'
) {
  const baseStructuredData = {
    '@context': 'https://schema.org',
  };

  switch (pageType) {
    case 'website':
      return {
        ...baseStructuredData,
        '@type': 'WebSite',
        name: SITE.name,
        description: SITE.description,
        url: SITE.url,
        potentialAction: {
          '@type': 'SearchAction',
          target: `${SITE.url}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      };

    case 'article':
      return {
        ...baseStructuredData,
        '@type': 'Article',
        headline: seo.title,
        description: seo.description,
        image: seo.image,
        author: {
          '@type': 'Person',
          name: seo.author,
        },
        publisher: {
          '@type': 'Organization',
          name: SITE.name,
          logo: {
            '@type': 'ImageObject',
            url: `${SITE.url}/logo.png`,
          },
        },
        datePublished: seo.publishedTime,
        dateModified: seo.modifiedTime,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': seo.canonical,
        },
      };

    case 'organization':
      return {
        ...baseStructuredData,
        '@type': 'Organization',
        name: SITE.name,
        description: SITE.description,
        url: SITE.url,
        logo: `${SITE.url}/logo.png`,
        sameAs: [SITE.social.twitter, SITE.social.github],
      };

    default:
      return baseStructuredData;
  }
}
